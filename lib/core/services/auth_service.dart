import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';
import '../models/customer_model.dart';
import '../models/craftsman_model.dart';
import '../repositories/customer_repository.dart';
import '../repositories/craftsman_repository.dart';
import '../repositories/auth_repository.dart';

/// خدمة المصادقة والتشفير
class AuthService {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  final CustomerRepository _customerRepository = CustomerRepository();
  final CraftsmanRepository _craftsmanRepository = CraftsmanRepository();
  final AuthRepository _authRepository = AuthRepository();

  // مفاتيح التخزين المحلي
  static const String _keyIsLoggedIn = 'is_logged_in';
  static const String _keyUserType = 'user_type';
  static const String _keyUserId = 'user_id';
  static const String _keyUserEmail = 'user_email';
  static const String _keyUserName = 'user_name';

  /// تشفير كلمة المرور
  String hashPassword(String password) {
    // إنشاء salt عشوائي
    final salt = _generateSalt();

    // دمج كلمة المرور مع الـ salt
    final saltedPassword = password + salt;

    // تشفير باستخدام SHA-256
    final bytes = utf8.encode(saltedPassword);
    final digest = sha256.convert(bytes);

    // إرجاع الـ hash مع الـ salt
    return '$salt:${digest.toString()}';
  }

  /// التحقق من كلمة المرور
  bool verifyPassword(String password, String hashedPassword) {
    try {
      // فصل الـ salt عن الـ hash
      final parts = hashedPassword.split(':');
      if (parts.length != 2) return false;

      final salt = parts[0];
      final hash = parts[1];

      // تشفير كلمة المرور المدخلة مع نفس الـ salt
      final saltedPassword = password + salt;
      final bytes = utf8.encode(saltedPassword);
      final digest = sha256.convert(bytes);

      // مقارنة النتيجة
      return digest.toString() == hash;
    } catch (e) {
      debugPrint('خطأ في التحقق من كلمة المرور: $e');
      return false;
    }
  }

  /// إنشاء salt عشوائي
  String _generateSalt() {
    final random = Random.secure();
    final saltBytes = List<int>.generate(16, (i) => random.nextInt(256));
    return base64.encode(saltBytes);
  }

  /// تسجيل عميل جديد
  Future<AuthResult> registerCustomer({
    required String name,
    required String email,
    required String phone,
    required String password,
    required String regionId,
    required String cityId,
  }) async {
    try {
      // التحقق من عدم وجود البريد الإلكتروني
      final existingCustomer =
          await _customerRepository.getCustomerByEmail(email);
      if (existingCustomer != null) {
        return AuthResult.failure('البريد الإلكتروني مستخدم مسبقاً');
      }

      // التحقق من عدم وجود البريد في جدول الحرفيين أيضاً
      final existingCraftsman =
          await _craftsmanRepository.getCraftsmanByEmail(email);
      if (existingCraftsman != null) {
        return AuthResult.failure('البريد الإلكتروني مستخدم مسبقاً');
      }

      // تشفير كلمة المرور
      final hashedPassword = hashPassword(password);

      // إنشاء نموذج العميل
      final customer = CustomerModel(
        name: name,
        email: email,
        phone: phone,
        regionId: regionId,
        cityId: cityId,
        createdAt: DateTime.now(),
      );

      // حفظ العميل في قاعدة البيانات
      final customerId = await _customerRepository.insertCustomer(customer);

      if (customerId == null) {
        return AuthResult.failure('فشل في إنشاء الحساب');
      }

      // حفظ كلمة المرور المشفرة
      await _authRepository.savePasswordHash(
        userId: customerId,
        userType: 'customer',
        passwordHash: hashedPassword,
      );

      return AuthResult.success(
        userId: customerId,
        userType: 'customer',
        email: email,
        name: name,
      );
    } catch (e) {
      debugPrint('خطأ في تسجيل العميل: $e');
      return AuthResult.failure('حدث خطأ أثناء التسجيل');
    }
  }

  /// تسجيل حرفي جديد
  Future<AuthResult> registerCraftsman({
    required String name,
    required String email,
    required String phone,
    required String password,
    required String craftId,
    required String regionId,
    required String cityId,
  }) async {
    try {
      // التحقق من عدم وجود البريد الإلكتروني
      final existingCraftsman =
          await _craftsmanRepository.getCraftsmanByEmail(email);
      if (existingCraftsman != null) {
        return AuthResult.failure('البريد الإلكتروني مستخدم مسبقاً');
      }

      // التحقق من عدم وجود البريد في جدول العملاء أيضاً
      final existingCustomer =
          await _customerRepository.getCustomerByEmail(email);
      if (existingCustomer != null) {
        return AuthResult.failure('البريد الإلكتروني مستخدم مسبقاً');
      }

      // تشفير كلمة المرور
      final hashedPassword = hashPassword(password);

      // إنشاء نموذج الحرفي
      final craftsman = CraftsmanModel(
        name: name,
        email: email,
        phone: phone,
        craftId: craftId,
        regionId: regionId,
        cityId: cityId,
        createdAt: DateTime.now(),
      );

      // حفظ الحرفي في قاعدة البيانات
      final craftsmanId = await _craftsmanRepository.insertCraftsman(craftsman);

      if (craftsmanId == null) {
        return AuthResult.failure('فشل في إنشاء الحساب');
      }

      // حفظ كلمة المرور المشفرة
      await _authRepository.savePasswordHash(
        userId: craftsmanId,
        userType: 'craftsman',
        passwordHash: hashedPassword,
      );

      return AuthResult.success(
        userId: craftsmanId,
        userType: 'craftsman',
        email: email,
        name: name,
      );
    } catch (e) {
      debugPrint('خطأ في تسجيل الحرفي: $e');
      return AuthResult.failure('حدث خطأ أثناء التسجيل');
    }
  }

  /// تسجيل الدخول
  Future<AuthResult> login({
    required String email,
    required String password,
    required String userType,
  }) async {
    try {
      if (userType == 'customer') {
        // البحث عن العميل
        final customer = await _customerRepository.getCustomerByEmail(email);
        if (customer == null) {
          return AuthResult.failure('البريد الإلكتروني غير موجود');
        }

        // التحقق من كلمة المرور
        final storedHash = await _authRepository.getPasswordHash(
          userId: customer.id!,
          userType: 'customer',
        );
        if (storedHash == null || !verifyPassword(password, storedHash)) {
          return AuthResult.failure('كلمة المرور غير صحيحة');
        }

        // حفظ جلسة المستخدم
        await _saveUserSession(
          userId: customer.id!,
          userType: 'customer',
          email: customer.email,
          name: customer.name,
        );

        return AuthResult.success(
          userId: customer.id!,
          userType: 'customer',
          email: customer.email,
          name: customer.name,
        );
      } else {
        // البحث عن الحرفي
        final craftsman = await _craftsmanRepository.getCraftsmanByEmail(email);
        if (craftsman == null) {
          return AuthResult.failure('البريد الإلكتروني غير موجود');
        }

        // التحقق من كلمة المرور
        final storedHash = await _authRepository.getPasswordHash(
          userId: craftsman.id!,
          userType: 'craftsman',
        );
        if (storedHash == null || !verifyPassword(password, storedHash)) {
          return AuthResult.failure('كلمة المرور غير صحيحة');
        }

        // حفظ جلسة المستخدم
        await _saveUserSession(
          userId: craftsman.id!,
          userType: 'craftsman',
          email: craftsman.email,
          name: craftsman.name,
        );

        return AuthResult.success(
          userId: craftsman.id!,
          userType: 'craftsman',
          email: craftsman.email,
          name: craftsman.name,
        );
      }
    } catch (e) {
      debugPrint('خطأ في تسجيل الدخول: $e');
      return AuthResult.failure('حدث خطأ أثناء تسجيل الدخول');
    }
  }

  /// تسجيل الخروج
  Future<void> logout() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.clear();
  }

  /// التحقق من حالة تسجيل الدخول
  Future<bool> isLoggedIn() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_keyIsLoggedIn) ?? false;
  }

  /// الحصول على معلومات المستخدم الحالي
  Future<UserSession?> getCurrentUser() async {
    final prefs = await SharedPreferences.getInstance();

    if (!(prefs.getBool(_keyIsLoggedIn) ?? false)) {
      return null;
    }

    return UserSession(
      userId: prefs.getInt(_keyUserId) ?? 0,
      userType: prefs.getString(_keyUserType) ?? '',
      email: prefs.getString(_keyUserEmail) ?? '',
      name: prefs.getString(_keyUserName) ?? '',
    );
  }

  /// حفظ جلسة المستخدم
  Future<void> _saveUserSession({
    required int userId,
    required String userType,
    required String email,
    required String name,
  }) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_keyIsLoggedIn, true);
    await prefs.setInt(_keyUserId, userId);
    await prefs.setString(_keyUserType, userType);
    await prefs.setString(_keyUserEmail, email);
    await prefs.setString(_keyUserName, name);
  }
}

/// نتيجة عملية المصادقة
class AuthResult {
  final bool isSuccess;
  final String? errorMessage;
  final int? userId;
  final String? userType;
  final String? email;
  final String? name;

  AuthResult._({
    required this.isSuccess,
    this.errorMessage,
    this.userId,
    this.userType,
    this.email,
    this.name,
  });

  factory AuthResult.success({
    required int userId,
    required String userType,
    required String email,
    required String name,
  }) {
    return AuthResult._(
      isSuccess: true,
      userId: userId,
      userType: userType,
      email: email,
      name: name,
    );
  }

  factory AuthResult.failure(String errorMessage) {
    return AuthResult._(
      isSuccess: false,
      errorMessage: errorMessage,
    );
  }
}

/// جلسة المستخدم
class UserSession {
  final int userId;
  final String userType;
  final String email;
  final String name;

  UserSession({
    required this.userId,
    required this.userType,
    required this.email,
    required this.name,
  });

  bool get isCustomer => userType == 'customer';
  bool get isCraftsman => userType == 'craftsman';
}
