import 'package:flutter/material.dart';
import '../database/database_helper.dart';
import '../utils/logger.dart';
import '../models/craft_model.dart';

/// مستودع الحرف
class CraftRepository {
  final DatabaseHelper _dbHelper = DatabaseHelper();

  /// الحصول على جميع فئات الحرف
  Future<List<CraftCategoryModel>> getAllCategories() async {
    final db = await _dbHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'craft_categories',
      where: 'isActive = ?',
      whereArgs: [1],
      orderBy: 'sortOrder ASC, nameAr ASC',
    );

    List<CraftCategoryModel> categories = [];
    for (var map in maps) {
      final crafts = await getCraftsByCategory(map['id']);
      categories.add(CraftCategoryModel(
        id: map['id'],
        nameAr: map['nameAr'],
        nameEn: map['nameEn'],
        nameFr: map['nameFr'],
        descriptionAr: map['descriptionAr'] ?? '',
        descriptionEn: map['descriptionEn'] ?? '',
        descriptionFr: map['descriptionFr'] ?? '',
        icon: _getIconFromName(map['iconName']),
        iconName: map['iconName'],
        color: Color(int.parse(map['colorHex'].replaceFirst('#', '0xFF'))),
        colorHex: map['colorHex'],
        crafts: crafts,
        isActive: map['isActive'] == 1,
        sortOrder: map['sortOrder'],
      ));
    }

    return categories;
  }

  /// الحصول على جميع الحرف
  Future<List<CraftModel>> getAllCrafts() async {
    final db = await _dbHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'crafts',
      where: 'isActive = ?',
      whereArgs: [1],
      orderBy: 'nameAr ASC',
    );

    return List.generate(maps.length, (i) {
      return CraftModel.fromMap(maps[i]);
    });
  }

  /// الحصول على الحرف حسب الفئة
  Future<List<CraftModel>> getCraftsByCategory(String categoryId) async {
    final db = await _dbHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'crafts',
      where: 'categoryId = ? AND isActive = ?',
      whereArgs: [categoryId, 1],
      orderBy: 'nameAr ASC',
    );

    return List.generate(maps.length, (i) {
      return CraftModel.fromMap(maps[i]);
    });
  }

  /// الحصول على الحرف الشائعة
  Future<List<CraftModel>> getPopularCrafts({int limit = 10}) async {
    final db = await _dbHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'crafts',
      where: 'isActive = ? AND isPopular = ?',
      whereArgs: [1, 1],
      orderBy: 'craftsmenCount DESC, averageRating DESC',
      limit: limit,
    );

    return List.generate(maps.length, (i) {
      return CraftModel.fromMap(maps[i]);
    });
  }

  /// البحث في الحرف
  Future<List<CraftModel>> searchCrafts(String query) async {
    final db = await _dbHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'crafts',
      where: '''
        (nameAr LIKE ? OR nameEn LIKE ? OR nameFr LIKE ? OR 
         descriptionAr LIKE ? OR descriptionEn LIKE ? OR descriptionFr LIKE ? OR
         keywords LIKE ?) 
        AND isActive = ?
      ''',
      whereArgs: [
        '%$query%',
        '%$query%',
        '%$query%',
        '%$query%',
        '%$query%',
        '%$query%',
        '%$query%',
        1
      ],
      orderBy: 'craftsmenCount DESC, nameAr ASC',
    );

    return List.generate(maps.length, (i) {
      return CraftModel.fromMap(maps[i]);
    });
  }

  /// الحصول على حرفة بالمعرف
  Future<CraftModel?> getCraftById(String id) async {
    final db = await _dbHelper.database;
    final List<Map<String, dynamic>> maps = await db.query(
      'crafts',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return CraftModel.fromMap(maps.first);
    }
    return null;
  }

  /// إضافة فئة حرف جديدة
  Future<int> insertCategory(CraftCategoryModel category) async {
    final db = await _dbHelper.database;
    return await db.insert('craft_categories', {
      'id': category.id,
      'nameAr': category.nameAr,
      'nameEn': category.nameEn,
      'nameFr': category.nameFr,
      'descriptionAr': category.descriptionAr,
      'descriptionEn': category.descriptionEn,
      'descriptionFr': category.descriptionFr,
      'iconName': category.iconName,
      'colorHex': category.colorHex,
      'isActive': category.isActive ? 1 : 0,
      'sortOrder': category.sortOrder,
      'createdAt': DateTime.now().toIso8601String(),
      'updatedAt': DateTime.now().toIso8601String(),
    });
  }

  /// إضافة حرفة جديدة
  Future<int> insertCraft(CraftModel craft) async {
    final db = await _dbHelper.database;
    return await db.insert('crafts', craft.toMap());
  }

  /// تحديث حرفة
  Future<int> updateCraft(CraftModel craft) async {
    final db = await _dbHelper.database;
    return await db.update(
      'crafts',
      craft.copyWith(updatedAt: DateTime.now()).toMap(),
      where: 'id = ?',
      whereArgs: [craft.id],
    );
  }

  /// تحديث عدد الحرفيين للحرفة
  Future<int> updateCraftsmenCount(String craftId) async {
    final db = await _dbHelper.database;

    // حساب عدد الحرفيين النشطين لهذه الحرفة
    final count = await db.rawQuery('''
      SELECT COUNT(*) as count 
      FROM craftsman_crafts cc
      JOIN craftsmen c ON cc.craftsmanId = c.id
      WHERE cc.craftId = ? AND c.isActive = 1 AND c.isAvailable = 1
    ''', [craftId]);

    final craftsmenCount = count.first['count'] as int;

    return await db.update(
      'crafts',
      {
        'craftsmenCount': craftsmenCount,
        'updatedAt': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [craftId],
    );
  }

  /// حذف حرفة (حذف منطقي)
  Future<int> deleteCraft(String id) async {
    final db = await _dbHelper.database;
    return await db.update(
      'crafts',
      {
        'isActive': 0,
        'updatedAt': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  /// الحصول على الحرف حسب المدينة
  Future<List<CraftModel>> getCraftsByCity(String cityId) async {
    final db = await _dbHelper.database;
    final List<Map<String, dynamic>> maps = await db.rawQuery('''
      SELECT DISTINCT c.*
      FROM crafts c
      JOIN craftsman_crafts cc ON c.id = cc.craftId
      JOIN craftsmen cm ON cc.craftsmanId = cm.id
      WHERE cm.cityId = ? AND c.isActive = 1 AND cm.isActive = 1 AND cm.isAvailable = 1
      ORDER BY c.craftsmenCount DESC, c.nameAr ASC
    ''', [cityId]);

    return List.generate(maps.length, (i) {
      return CraftModel.fromMap(maps[i]);
    });
  }

  /// الحصول على إحصائيات الحرفة
  Future<Map<String, dynamic>> getCraftStats(String craftId) async {
    final db = await _dbHelper.database;

    final stats = await db.rawQuery('''
      SELECT 
        COUNT(DISTINCT cc.craftsmanId) as craftsmenCount,
        AVG(c.averageRating) as averageRating,
        COUNT(DISTINCT c.cityId) as citiesCount
      FROM craftsman_crafts cc
      JOIN craftsmen c ON cc.craftsmanId = c.id
      WHERE cc.craftId = ? AND c.isActive = 1
    ''', [craftId]);

    return stats.first;
  }

  /// إدراج البيانات الأولية للحرف
  Future<void> insertInitialCrafts() async {
    // سيتم تنفيذ هذا في ملف منفصل للبيانات الأولية
    AppLogger.info('✅ تم إدراج الحرف الأولية');
  }

  /// الحصول على الأيقونة من الاسم
  IconData _getIconFromName(String iconName) {
    switch (iconName) {
      case 'build':
        return Icons.build;
      case 'handyman':
        return Icons.handyman;
      case 'electrical_services':
        return Icons.electrical_services;
      case 'plumbing':
        return Icons.plumbing;
      case 'carpenter':
        return Icons.carpenter;
      case 'paint_brush':
        return Icons.brush;
      case 'cleaning_services':
        return Icons.cleaning_services;
      case 'garden':
        return Icons.grass;
      case 'car_repair':
        return Icons.car_repair;
      case 'computer':
        return Icons.computer;
      case 'phone_android':
        return Icons.phone_android;
      case 'kitchen':
        return Icons.kitchen;
      case 'home_repair_service':
        return Icons.home_repair_service;
      case 'design_services':
        return Icons.design_services;
      case 'camera_alt':
        return Icons.camera_alt;
      default:
        return Icons.handyman;
    }
  }
}
