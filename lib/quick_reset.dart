import 'package:flutter/material.dart';
import 'core/database/database_helper.dart';
import 'core/services/database_service.dart';

/// إعادة تعيين سريعة لقاعدة البيانات
class QuickDatabaseReset {
  static Future<void> resetIfNeeded() async {
    try {
      debugPrint('🔄 فحص قاعدة البيانات...');

      final dbHelper = DatabaseHelper();
      final db = await dbHelper.database;

      // فحص عدد المدن الموجودة
      final result = await db.rawQuery('SELECT COUNT(*) as count FROM cities');
      final cityCount = result.first['count'] as int;

      debugPrint('📊 عدد المدن الحالي: $cityCount');

      // إذا كان عدد المدن أقل من 200، أعد تعيين قاعدة البيانات
      if (cityCount < 200) {
        debugPrint('⚠️ عدد المدن قليل، سيتم إعادة تعيين قاعدة البيانات...');

        // حذف قاعدة البيانات القديمة
        await dbHelper.deleteDatabase();
        debugPrint('🗑️ تم حذف قاعدة البيانات القديمة');

        // إعادة تهيئة قاعدة البيانات
        final dbService = DatabaseService();
        await dbService.initialize();
        debugPrint('✅ تم إنشاء قاعدة البيانات الجديدة مع 200 مدينة');

        // التحقق من النتيجة
        final newDb = await dbHelper.database;
        final newResult =
            await newDb.rawQuery('SELECT COUNT(*) as count FROM cities');
        final newCityCount = newResult.first['count'] as int;
        debugPrint('🎉 عدد المدن الجديد: $newCityCount');
      } else {
        debugPrint('✅ قاعدة البيانات محدثة، لا حاجة لإعادة التعيين');
      }
    } catch (e) {
      debugPrint('❌ خطأ في فحص/إعادة تعيين قاعدة البيانات: $e');
      // في حالة الخطأ، أعد تعيين قاعدة البيانات
      try {
        final dbHelper = DatabaseHelper();
        await dbHelper.deleteDatabase();

        final dbService = DatabaseService();
        await dbService.initialize();
        debugPrint('🔧 تم إعادة تعيين قاعدة البيانات بعد الخطأ');
      } catch (resetError) {
        debugPrint('💥 فشل في إعادة تعيين قاعدة البيانات: $resetError');
        rethrow;
      }
    }
  }

  /// إعادة تعيين إجبارية لقاعدة البيانات
  static Future<void> forceReset() async {
    try {
      debugPrint('🔄 بدء إعادة التعيين الإجبارية...');

      final dbHelper = DatabaseHelper();

      // حذف قاعدة البيانات القديمة
      await dbHelper.deleteDatabase();
      debugPrint('🗑️ تم حذف قاعدة البيانات القديمة');

      // إعادة تهيئة قاعدة البيانات
      final dbService = DatabaseService();
      await dbService.initialize();
      debugPrint('✅ تم إنشاء قاعدة البيانات الجديدة');

      // التحقق من النتيجة
      final newDb = await dbHelper.database;
      final result =
          await newDb.rawQuery('SELECT COUNT(*) as count FROM cities');
      final cityCount = result.first['count'] as int;
      debugPrint('🎉 عدد المدن: $cityCount');
    } catch (e) {
      debugPrint('❌ خطأ في إعادة التعيين الإجبارية: $e');
      rethrow;
    }
  }

  /// فحص حالة قاعدة البيانات
  static Future<Map<String, dynamic>> checkDatabaseStatus() async {
    try {
      final dbHelper = DatabaseHelper();
      final db = await dbHelper.database;

      // فحص عدد الجداول
      final tablesResult = await db.rawQuery(
          "SELECT COUNT(*) as count FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'");
      final tableCount = tablesResult.first['count'] as int;

      // فحص عدد المدن
      final citiesResult =
          await db.rawQuery('SELECT COUNT(*) as count FROM cities');
      final cityCount = citiesResult.first['count'] as int;

      // فحص عدد الجهات
      final regionsResult =
          await db.rawQuery('SELECT COUNT(*) as count FROM regions');
      final regionCount = regionsResult.first['count'] as int;

      // فحص عدد الحرف
      final craftsResult =
          await db.rawQuery('SELECT COUNT(*) as count FROM crafts');
      final craftCount = craftsResult.first['count'] as int;

      return {
        'tables': tableCount,
        'cities': cityCount,
        'regions': regionCount,
        'crafts': craftCount,
        'is_healthy': tableCount >= 6 && cityCount >= 200 && regionCount >= 12,
      };
    } catch (e) {
      debugPrint('❌ خطأ في فحص حالة قاعدة البيانات: $e');
      return {
        'tables': 0,
        'cities': 0,
        'regions': 0,
        'crafts': 0,
        'is_healthy': false,
        'error': e.toString(),
      };
    }
  }
}
