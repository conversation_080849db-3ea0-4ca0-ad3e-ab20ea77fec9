import 'package:flutter/material.dart';
import 'core/services/database_service.dart';
import 'core/services/auth_service.dart';
import 'core/repositories/customer_repository.dart';
import 'core/repositories/craftsman_repository.dart';

/// فحص سريع لبيانات المستخدمين
class QuickUserCheck {
  static Future<Map<String, dynamic>> checkUserData() async {
    try {
      final dbService = DatabaseService();
      await dbService.initialize();
      
      final customerRepo = CustomerRepository();
      final craftsmanRepo = CraftsmanRepository();
      final authService = AuthService();
      
      // فحص الجلسة الحالية
      final isLoggedIn = await authService.isLoggedIn();
      final currentUser = isLoggedIn ? await authService.getCurrentUser() : null;
      
      // فحص عدد المستخدمين
      final customers = await customerRepo.getAllCustomers();
      final craftsmen = await craftsmanRepo.getAllCraftsmen();
      
      // فحص قاعدة البيانات مباشرة
      final dbHelper = dbService.databaseHelper;
      final db = await dbHelper.database;
      
      final customerCount = await db.rawQuery('SELECT COUNT(*) as count FROM customers');
      final craftsmanCount = await db.rawQuery('SELECT COUNT(*) as count FROM craftsmen');
      final authCount = await db.rawQuery('SELECT COUNT(*) as count FROM user_auth');
      final cityCount = await db.rawQuery('SELECT COUNT(*) as count FROM cities');
      final regionCount = await db.rawQuery('SELECT COUNT(*) as count FROM regions');
      final craftCount = await db.rawQuery('SELECT COUNT(*) as count FROM crafts');
      
      return {
        'session': {
          'isLoggedIn': isLoggedIn,
          'currentUser': currentUser != null ? {
            'userId': currentUser.userId,
            'userType': currentUser.userType,
            'email': currentUser.email,
            'name': currentUser.name,
          } : null,
        },
        'users': {
          'customers': customers.length,
          'craftsmen': craftsmen.length,
        },
        'database': {
          'customers': customerCount.first['count'],
          'craftsmen': craftsmanCount.first['count'],
          'auth_records': authCount.first['count'],
          'cities': cityCount.first['count'],
          'regions': regionCount.first['count'],
          'crafts': craftCount.first['count'],
        },
        'sample_data': {
          'first_customer': customers.isNotEmpty ? {
            'id': customers.first.id,
            'name': customers.first.name,
            'email': customers.first.email,
            'phone': customers.first.phone,
            'regionId': customers.first.regionId,
            'cityId': customers.first.cityId,
          } : null,
          'first_craftsman': craftsmen.isNotEmpty ? {
            'id': craftsmen.first.id,
            'name': craftsmen.first.name,
            'email': craftsmen.first.email,
            'phone': craftsmen.first.phone,
            'craftId': craftsmen.first.craftId,
            'regionId': craftsmen.first.regionId,
            'cityId': craftsmen.first.cityId,
            'rating': craftsmen.first.rating,
            'completedOrders': craftsmen.first.completedOrders,
            'isActive': craftsmen.first.isActive,
          } : null,
        },
        'status': 'success',
        'timestamp': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      return {
        'status': 'error',
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }
  
  /// طباعة تقرير سريع
  static Future<void> printQuickReport() async {
    debugPrint('🔍 فحص سريع لبيانات المستخدمين...');
    
    final result = await checkUserData();
    
    if (result['status'] == 'error') {
      debugPrint('❌ خطأ في الفحص: ${result['error']}');
      return;
    }
    
    debugPrint('\n📊 تقرير سريع:');
    debugPrint('================');
    
    // الجلسة الحالية
    final session = result['session'];
    debugPrint('🔐 الجلسة الحالية:');
    debugPrint('   - مسجل الدخول: ${session['isLoggedIn']}');
    if (session['currentUser'] != null) {
      final user = session['currentUser'];
      debugPrint('   - المستخدم: ${user['name']} (${user['userType']})');
      debugPrint('   - البريد: ${user['email']}');
      debugPrint('   - المعرف: ${user['userId']}');
    }
    
    // إحصائيات المستخدمين
    final users = result['users'];
    final database = result['database'];
    debugPrint('\n👥 إحصائيات المستخدمين:');
    debugPrint('   - العملاء: ${users['customers']} (قاعدة البيانات: ${database['customers']})');
    debugPrint('   - الحرفيين: ${users['craftsmen']} (قاعدة البيانات: ${database['craftsmen']})');
    debugPrint('   - بيانات المصادقة: ${database['auth_records']}');
    
    // إحصائيات قاعدة البيانات
    debugPrint('\n🗄️ إحصائيات قاعدة البيانات:');
    debugPrint('   - الجهات: ${database['regions']}');
    debugPrint('   - المدن: ${database['cities']}');
    debugPrint('   - الحرف: ${database['crafts']}');
    
    // عينة من البيانات
    final sampleData = result['sample_data'];
    if (sampleData['first_customer'] != null) {
      final customer = sampleData['first_customer'];
      debugPrint('\n👤 أول عميل:');
      debugPrint('   - الاسم: ${customer['name']}');
      debugPrint('   - البريد: ${customer['email']}');
      debugPrint('   - الهاتف: ${customer['phone']}');
      debugPrint('   - الجهة/المدينة: ${customer['regionId']}/${customer['cityId']}');
    } else {
      debugPrint('\n👤 لا يوجد عملاء');
    }
    
    if (sampleData['first_craftsman'] != null) {
      final craftsman = sampleData['first_craftsman'];
      debugPrint('\n🔨 أول حرفي:');
      debugPrint('   - الاسم: ${craftsman['name']}');
      debugPrint('   - البريد: ${craftsman['email']}');
      debugPrint('   - الهاتف: ${craftsman['phone']}');
      debugPrint('   - الحرفة: ${craftsman['craftId']}');
      debugPrint('   - التقييم: ${craftsman['rating']}');
      debugPrint('   - الطلبات المكتملة: ${craftsman['completedOrders']}');
      debugPrint('   - نشط: ${craftsman['isActive']}');
    } else {
      debugPrint('\n🔨 لا يوجد حرفيين');
    }
    
    debugPrint('\n✅ انتهى الفحص السريع');
  }
}

/// ويدجت عرض حالة بيانات المستخدمين
class UserDataStatusWidget extends StatefulWidget {
  const UserDataStatusWidget({super.key});

  @override
  State<UserDataStatusWidget> createState() => _UserDataStatusWidgetState();
}

class _UserDataStatusWidgetState extends State<UserDataStatusWidget> {
  Map<String, dynamic>? _userData;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  Future<void> _loadUserData() async {
    setState(() {
      _isLoading = true;
    });

    final data = await QuickUserCheck.checkUserData();
    
    setState(() {
      _userData = data;
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('جاري فحص بيانات المستخدمين...'),
            ],
          ),
        ),
      );
    }

    if (_userData == null || _userData!['status'] == 'error') {
      return Card(
        color: Colors.red[50],
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Row(
                children: [
                  Icon(Icons.error, color: Colors.red),
                  SizedBox(width: 8),
                  Text(
                    'خطأ في فحص بيانات المستخدمين',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.red,
                    ),
                  ),
                ],
              ),
              if (_userData?['error'] != null) ...[
                const SizedBox(height: 8),
                Text(
                  'الخطأ: ${_userData!['error']}',
                  style: const TextStyle(color: Colors.red),
                ),
              ],
              const SizedBox(height: 8),
              ElevatedButton(
                onPressed: _loadUserData,
                child: const Text('إعادة المحاولة'),
              ),
            ],
          ),
        ),
      );
    }

    final session = _userData!['session'];
    final users = _userData!['users'];
    final database = _userData!['database'];

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                const Text(
                  'حالة بيانات المستخدمين',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                IconButton(
                  onPressed: _loadUserData,
                  icon: const Icon(Icons.refresh),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // الجلسة الحالية
            _buildStatusRow(
              'الجلسة الحالية',
              session['isLoggedIn'] ? 'مسجل الدخول' : 'غير مسجل',
              session['isLoggedIn'] ? Colors.green : Colors.orange,
            ),
            
            if (session['currentUser'] != null) ...[
              const SizedBox(height: 8),
              Text(
                'المستخدم: ${session['currentUser']['name']} (${session['currentUser']['userType']})',
                style: const TextStyle(fontSize: 14),
              ),
            ],
            
            const SizedBox(height: 16),
            
            // إحصائيات المستخدمين
            _buildStatusRow(
              'العملاء',
              '${users['customers']} مستخدم',
              users['customers'] > 0 ? Colors.green : Colors.red,
            ),
            
            _buildStatusRow(
              'الحرفيين',
              '${users['craftsmen']} مستخدم',
              users['craftsmen'] > 0 ? Colors.green : Colors.red,
            ),
            
            _buildStatusRow(
              'بيانات المصادقة',
              '${database['auth_records']} سجل',
              database['auth_records'] > 0 ? Colors.green : Colors.red,
            ),
            
            const SizedBox(height: 16),
            
            // إحصائيات قاعدة البيانات
            Row(
              children: [
                Expanded(
                  child: _buildSmallStatusCard(
                    'الجهات',
                    '${database['regions']}',
                    database['regions'] >= 12,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildSmallStatusCard(
                    'المدن',
                    '${database['cities']}',
                    database['cities'] >= 200,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildSmallStatusCard(
                    'الحرف',
                    '${database['crafts']}',
                    database['crafts'] >= 30,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusRow(String label, String value, Color color) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: color.withOpacity(0.3)),
            ),
            child: Text(
              value,
              style: TextStyle(
                color: color,
                fontWeight: FontWeight.w600,
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSmallStatusCard(String label, String value, bool isGood) {
    final color = isGood ? Colors.green : Colors.orange;
    
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            label,
            style: TextStyle(
              fontSize: 12,
              color: color,
            ),
          ),
        ],
      ),
    );
  }
}
