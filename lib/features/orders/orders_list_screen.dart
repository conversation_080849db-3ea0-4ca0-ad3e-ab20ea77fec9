import 'package:flutter/material.dart';
import '../../shared/constants/app_colors.dart';
import '../../core/models/order_model.dart';
import '../../core/repositories/order_repository.dart';
import 'order_details_screen.dart';

class OrdersListScreen extends StatefulWidget {
  final int userId;
  final bool isCustomerView;

  const OrdersListScreen({
    super.key,
    required this.userId,
    this.isCustomerView = true,
  });

  @override
  State<OrdersListScreen> createState() => _OrdersListScreenState();
}

class _OrdersListScreenState extends State<OrdersListScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final _orderRepository = OrderRepository();
  
  List<OrderModel> _allOrders = [];
  List<OrderModel> _pendingOrders = [];
  List<OrderModel> _activeOrders = [];
  List<OrderModel> _completedOrders = [];
  
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadOrders();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.isCustomerView ? 'طلباتي' : 'الطلبات الواردة'),
        backgroundColor: AppColors.primaryColor,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          indicatorColor: Colors.white,
          tabs: [
            Tab(
              text: 'الكل',
              icon: Badge(
                label: Text('${_allOrders.length}'),
                child: const Icon(Icons.list),
              ),
            ),
            Tab(
              text: 'معلقة',
              icon: Badge(
                label: Text('${_pendingOrders.length}'),
                child: const Icon(Icons.hourglass_empty),
              ),
            ),
            Tab(
              text: 'نشطة',
              icon: Badge(
                label: Text('${_activeOrders.length}'),
                child: const Icon(Icons.work),
              ),
            ),
            Tab(
              text: 'مكتملة',
              icon: Badge(
                label: Text('${_completedOrders.length}'),
                child: const Icon(Icons.check_circle),
              ),
            ),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildOrdersList(_allOrders),
                _buildOrdersList(_pendingOrders),
                _buildOrdersList(_activeOrders),
                _buildOrdersList(_completedOrders),
              ],
            ),
    );
  }

  Widget _buildOrdersList(List<OrderModel> orders) {
    if (orders.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.inbox_outlined,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد طلبات',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadOrders,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: orders.length,
        itemBuilder: (context, index) {
          final order = orders[index];
          return _buildOrderCard(order);
        },
      ),
    );
  }

  Widget _buildOrderCard(OrderModel order) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () => _navigateToOrderDetails(order),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // العنوان والحالة
              Row(
                children: [
                  Expanded(
                    child: Text(
                      order.title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  const SizedBox(width: 8),
                  _buildStatusChip(order.status),
                ],
              ),
              
              const SizedBox(height: 8),
              
              // الوصف
              Text(
                order.description,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              
              const SizedBox(height: 12),
              
              // معلومات إضافية
              Row(
                children: [
                  Icon(
                    Icons.calendar_today,
                    size: 16,
                    color: Colors.grey[500],
                  ),
                  const SizedBox(width: 4),
                  Text(
                    _formatDate(order.createdAt),
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[500],
                    ),
                  ),
                  
                  const SizedBox(width: 16),
                  
                  Icon(
                    Icons.attach_money,
                    size: 16,
                    color: Colors.grey[500],
                  ),
                  const SizedBox(width: 4),
                  Text(
                    order.priceText,
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[500],
                    ),
                  ),
                  
                  const Spacer(),
                  
                  if (order.customerRating != null) ...[
                    Icon(
                      Icons.star,
                      size: 16,
                      color: Colors.amber,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      order.customerRating!.toStringAsFixed(1),
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ],
              ),
              
              // أزرار سريعة للإجراءات
              if (_shouldShowQuickActions(order)) ...[
                const SizedBox(height: 12),
                _buildQuickActions(order),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatusChip(OrderStatus status) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Color(int.parse(status.name == 'pending' ? '0xFFFF9800' :
                               status.name == 'accepted' ? '0xFF2196F3' :
                               status.name == 'inProgress' ? '0xFFFF9800' :
                               status.name == 'completed' ? '0xFF4CAF50' :
                               status.name == 'cancelled' ? '0xFF9E9E9E' :
                               '0xFFF44336')),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        _getStatusText(status),
        style: const TextStyle(
          color: Colors.white,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildQuickActions(OrderModel order) {
    List<Widget> actions = [];

    if (widget.isCustomerView) {
      if (order.canBeCancelled) {
        actions.add(
          TextButton.icon(
            onPressed: () => _quickUpdateStatus(order, OrderStatus.cancelled),
            icon: const Icon(Icons.cancel, size: 16),
            label: const Text('إلغاء'),
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
          ),
        );
      }
      
      if (order.canBeRated) {
        actions.add(
          TextButton.icon(
            onPressed: () => _navigateToOrderDetails(order),
            icon: const Icon(Icons.star, size: 16),
            label: const Text('تقييم'),
            style: TextButton.styleFrom(
              foregroundColor: Colors.amber,
            ),
          ),
        );
      }
    } else {
      if (order.status == OrderStatus.pending) {
        actions.addAll([
          TextButton.icon(
            onPressed: () => _quickUpdateStatus(order, OrderStatus.accepted),
            icon: const Icon(Icons.check, size: 16),
            label: const Text('قبول'),
            style: TextButton.styleFrom(
              foregroundColor: Colors.green,
            ),
          ),
          TextButton.icon(
            onPressed: () => _quickUpdateStatus(order, OrderStatus.rejected),
            icon: const Icon(Icons.close, size: 16),
            label: const Text('رفض'),
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
          ),
        ]);
      } else if (order.status == OrderStatus.accepted) {
        actions.add(
          TextButton.icon(
            onPressed: () => _quickUpdateStatus(order, OrderStatus.inProgress),
            icon: const Icon(Icons.play_arrow, size: 16),
            label: const Text('بدء'),
            style: TextButton.styleFrom(
              foregroundColor: AppColors.primaryColor,
            ),
          ),
        );
      } else if (order.status == OrderStatus.inProgress) {
        actions.add(
          TextButton.icon(
            onPressed: () => _quickUpdateStatus(order, OrderStatus.completed),
            icon: const Icon(Icons.check_circle, size: 16),
            label: const Text('إكمال'),
            style: TextButton.styleFrom(
              foregroundColor: Colors.green,
            ),
          ),
        );
      }
    }

    if (actions.isEmpty) return const SizedBox.shrink();

    return Row(
      children: actions
          .expand((action) => [action, const SizedBox(width: 8)])
          .take(actions.length * 2 - 1)
          .toList(),
    );
  }

  bool _shouldShowQuickActions(OrderModel order) {
    if (widget.isCustomerView) {
      return order.canBeCancelled || order.canBeRated;
    } else {
      return order.status == OrderStatus.pending ||
             order.status == OrderStatus.accepted ||
             order.status == OrderStatus.inProgress;
    }
  }

  String _getStatusText(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return 'معلق';
      case OrderStatus.accepted:
        return 'مقبول';
      case OrderStatus.inProgress:
        return 'جاري';
      case OrderStatus.completed:
        return 'مكتمل';
      case OrderStatus.cancelled:
        return 'ملغي';
      case OrderStatus.rejected:
        return 'مرفوض';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  Future<void> _loadOrders() async {
    setState(() {
      _isLoading = true;
    });

    try {
      List<OrderModel> orders;
      
      if (widget.isCustomerView) {
        orders = await _orderRepository.getCustomerOrders(widget.userId);
      } else {
        orders = await _orderRepository.getCraftsmanOrders(widget.userId);
      }

      setState(() {
        _allOrders = orders;
        _pendingOrders = orders.where((o) => o.status == OrderStatus.pending).toList();
        _activeOrders = orders.where((o) => 
          o.status == OrderStatus.accepted || o.status == OrderStatus.inProgress
        ).toList();
        _completedOrders = orders.where((o) => 
          o.status == OrderStatus.completed || 
          o.status == OrderStatus.cancelled || 
          o.status == OrderStatus.rejected
        ).toList();
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل الطلبات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _quickUpdateStatus(OrderModel order, OrderStatus newStatus) async {
    try {
      final success = await _orderRepository.updateOrderStatus(order.id!, newStatus);
      
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم تحديث حالة الطلب بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
        _loadOrders(); // إعادة تحميل القائمة
      } else {
        throw Exception('فشل في تحديث حالة الطلب');
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في تحديث الطلب: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _navigateToOrderDetails(OrderModel order) async {
    final result = await Navigator.of(context).push<bool>(
      MaterialPageRoute(
        builder: (context) => OrderDetailsScreen(
          order: order,
          isCustomerView: widget.isCustomerView,
        ),
      ),
    );

    if (result == true) {
      _loadOrders(); // إعادة تحميل القائمة إذا تم تحديث الطلب
    }
  }
}
