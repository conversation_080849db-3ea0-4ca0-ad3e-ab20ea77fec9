import 'package:flutter/material.dart';
import '../../features/splash/splash_screen.dart';
import '../../features/auth/user_type_selection_screen.dart';
import '../../features/auth/customer_login_screen.dart';
import '../../features/auth/customer_register_screen.dart';
import '../../features/auth/craftsman_login_screen.dart';
import '../../features/auth/craftsman_register_screen.dart';

/// مسارات التطبيق
class AppRoutes {
  // أسماء المسارات
  static const String splash = '/';
  static const String userTypeSelection = '/user-type-selection';

  // مسارات العملاء
  static const String customerLogin = '/customer/login';
  static const String customerRegister = '/customer/register';
  static const String customerHome = '/customer/home';
  static const String customerProfile = '/customer/profile';
  static const String customerSearch = '/customer/search';
  static const String customerBookings = '/customer/bookings';
  static const String customerFavorites = '/customer/favorites';
  static const String customerSettings = '/customer/settings';

  // مسارات الحرفيين
  static const String craftsmanLogin = '/craftsman/login';
  static const String craftsmanRegister = '/craftsman/register';
  static const String craftsmanHome = '/craftsman/home';
  static const String craftsmanProfile = '/craftsman/profile';
  static const String craftsmanOrders = '/craftsman/orders';
  static const String craftsmanEarnings = '/craftsman/earnings';
  static const String craftsmanSettings = '/craftsman/settings';

  // مسارات مشتركة
  static const String chat = '/chat';
  static const String notifications = '/notifications';
  static const String help = '/help';
  static const String about = '/about';
  static const String privacy = '/privacy';
  static const String terms = '/terms';

  /// خريطة المسارات
  static Map<String, WidgetBuilder> get routes => {
        splash: (context) => const SplashScreen(),
        userTypeSelection: (context) => const UserTypeSelectionScreen(),

        // مسارات العملاء
        customerLogin: (context) => const CustomerLoginScreen(),
        customerRegister: (context) => const CustomerRegisterScreen(),

        // مسارات الحرفيين
        craftsmanLogin: (context) => const CraftsmanLoginScreen(),
        craftsmanRegister: (context) => const CraftsmanRegisterScreen(),
      };

  /// معالج المسارات المولدة
  static Route<dynamic>? onGenerateRoute(RouteSettings settings) {
    switch (settings.name) {
      case splash:
        return _buildRoute(const SplashScreen(), settings);

      case userTypeSelection:
        return _buildRoute(const UserTypeSelectionScreen(), settings);

      // مسارات العملاء
      case customerLogin:
        return _buildRoute(const CustomerLoginScreen(), settings);

      case customerRegister:
        return _buildRoute(const CustomerRegisterScreen(), settings);

      // مسارات الحرفيين
      case craftsmanLogin:
        return _buildRoute(const CraftsmanLoginScreen(), settings);

      case craftsmanRegister:
        return _buildRoute(const CraftsmanRegisterScreen(), settings);

      default:
        return null;
    }
  }

  /// معالج المسارات غير المعروفة
  static Route<dynamic> onUnknownRoute(RouteSettings settings) {
    return _buildRoute(
      const SplashScreen(),
      settings,
    );
  }

  /// بناء المسار مع الرسوم المتحركة
  static PageRoute _buildRoute(Widget page, RouteSettings settings) {
    return PageRouteBuilder(
      settings: settings,
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        // رسوم متحركة للانزلاق من اليمين إلى اليسار (مناسب للعربية)
        const begin = Offset(1.0, 0.0);
        const end = Offset.zero;
        const curve = Curves.easeInOut;

        var tween = Tween(begin: begin, end: end).chain(
          CurveTween(curve: curve),
        );

        return SlideTransition(
          position: animation.drive(tween),
          child: child,
        );
      },
      transitionDuration: const Duration(milliseconds: 300),
    );
  }

  /// التنقل إلى صفحة جديدة
  static Future<T?> navigateTo<T>(BuildContext context, String routeName,
      {Object? arguments}) {
    return Navigator.pushNamed<T>(context, routeName, arguments: arguments);
  }

  /// التنقل إلى صفحة جديدة مع استبدال الحالية
  static Future<T?> navigateToReplacement<T extends Object?>(
      BuildContext context, String routeName,
      {Object? arguments}) {
    return Navigator.pushReplacementNamed<T, T>(context, routeName,
        arguments: arguments);
  }

  /// التنقل إلى صفحة جديدة مع مسح جميع الصفحات السابقة
  static Future<T?> navigateToAndClearStack<T extends Object?>(
      BuildContext context, String routeName,
      {Object? arguments}) {
    return Navigator.pushNamedAndRemoveUntil<T>(
      context,
      routeName,
      (route) => false,
      arguments: arguments,
    );
  }

  /// العودة إلى الصفحة السابقة
  static void goBack(BuildContext context, [dynamic result]) {
    Navigator.pop(context, result);
  }

  /// التحقق من إمكانية العودة
  static bool canGoBack(BuildContext context) {
    return Navigator.canPop(context);
  }

  /// الحصول على المسار الحالي
  static String? getCurrentRoute(BuildContext context) {
    return ModalRoute.of(context)?.settings.name;
  }

  /// التحقق من نوع المستخدم وتوجيهه للمسار المناسب
  static String getHomeRouteForUserType(String userType) {
    switch (userType) {
      case 'customer':
        return customerHome;
      case 'craftsman':
        return craftsmanHome;
      default:
        return userTypeSelection;
    }
  }

  /// التحقق من نوع المستخدم وتوجيهه لصفحة تسجيل الدخول المناسبة
  static String getLoginRouteForUserType(String userType) {
    switch (userType) {
      case 'customer':
        return customerLogin;
      case 'craftsman':
        return craftsmanLogin;
      default:
        return userTypeSelection;
    }
  }

  /// التحقق من نوع المستخدم وتوجيهه لصفحة التسجيل المناسبة
  static String getRegisterRouteForUserType(String userType) {
    switch (userType) {
      case 'customer':
        return customerRegister;
      case 'craftsman':
        return craftsmanRegister;
      default:
        return userTypeSelection;
    }
  }
}
