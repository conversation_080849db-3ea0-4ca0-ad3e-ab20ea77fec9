import 'package:flutter/material.dart';
import 'core/services/database_service.dart';
import 'core/services/auth_service.dart';
import 'core/repositories/customer_repository.dart';
import 'core/repositories/craftsman_repository.dart';
import 'core/repositories/auth_repository.dart';

/// أداة تشخيص بيانات المستخدمين
class UserDataDebugger {
  static Future<void> debugUserData() async {
    debugPrint('🔍 بدء تشخيص بيانات المستخدمين...');
    
    try {
      final dbService = DatabaseService();
      await dbService.initialize();
      
      final customerRepo = CustomerRepository();
      final craftsmanRepo = CraftsmanRepository();
      final authRepo = AuthRepository();
      final authService = AuthService();
      
      // 1. فحص الجلسة الحالية
      debugPrint('\n📱 فحص الجلسة الحالية:');
      final isLoggedIn = await authService.isLoggedIn();
      debugPrint('   - مسجل الدخول: $isLoggedIn');
      
      if (isLoggedIn) {
        final currentUser = await authService.getCurrentUser();
        if (currentUser != null) {
          debugPrint('   - معرف المستخدم: ${currentUser.userId}');
          debugPrint('   - نوع المستخدم: ${currentUser.userType}');
          debugPrint('   - البريد الإلكتروني: ${currentUser.email}');
          debugPrint('   - الاسم: ${currentUser.name}');
        } else {
          debugPrint('   - ❌ لا توجد بيانات جلسة');
        }
      }
      
      // 2. فحص عدد العملاء
      debugPrint('\n👥 فحص العملاء:');
      final customers = await customerRepo.getAllCustomers();
      debugPrint('   - عدد العملاء: ${customers.length}');
      
      if (customers.isNotEmpty) {
        debugPrint('   - أول عميل: ${customers.first.name} (${customers.first.email})');
        debugPrint('   - آخر عميل: ${customers.last.name} (${customers.last.email})');
      }
      
      // 3. فحص عدد الحرفيين
      debugPrint('\n🔨 فحص الحرفيين:');
      final craftsmen = await craftsmanRepo.getAllCraftsmen();
      debugPrint('   - عدد الحرفيين: ${craftsmen.length}');
      
      if (craftsmen.isNotEmpty) {
        debugPrint('   - أول حرفي: ${craftsmen.first.name} (${craftsmen.first.email})');
        debugPrint('   - آخر حرفي: ${craftsmen.last.name} (${craftsmen.last.email})');
      }
      
      // 4. فحص بيانات المصادقة
      debugPrint('\n🔐 فحص بيانات المصادقة:');
      final authStats = await authRepo.getAuthStatistics();
      debugPrint('   - إجمالي المستخدمين: ${authStats['total_users'] ?? 0}');
      debugPrint('   - العملاء: ${authStats['total_customers'] ?? 0}');
      debugPrint('   - الحرفيين: ${authStats['total_craftsmen'] ?? 0}');
      debugPrint('   - جدد هذا الشهر: ${authStats['new_this_month'] ?? 0}');
      
      // 5. فحص قاعدة البيانات المباشر
      debugPrint('\n🗄️ فحص قاعدة البيانات المباشر:');
      final dbHelper = dbService.databaseHelper;
      final db = await dbHelper.database;
      
      final customerCount = await db.rawQuery('SELECT COUNT(*) as count FROM customers');
      final craftsmanCount = await db.rawQuery('SELECT COUNT(*) as count FROM craftsmen');
      final authCount = await db.rawQuery('SELECT COUNT(*) as count FROM user_auth');
      
      debugPrint('   - عدد العملاء في قاعدة البيانات: ${customerCount.first['count']}');
      debugPrint('   - عدد الحرفيين في قاعدة البيانات: ${craftsmanCount.first['count']}');
      debugPrint('   - عدد بيانات المصادقة: ${authCount.first['count']}');
      
      // 6. عرض عينة من البيانات
      if (customers.isNotEmpty) {
        debugPrint('\n📋 عينة من بيانات العملاء:');
        for (int i = 0; i < customers.length && i < 3; i++) {
          final customer = customers[i];
          debugPrint('   ${i + 1}. ${customer.name}');
          debugPrint('      - البريد: ${customer.email}');
          debugPrint('      - الهاتف: ${customer.phone}');
          debugPrint('      - الجهة: ${customer.regionId}');
          debugPrint('      - المدينة: ${customer.cityId}');
          debugPrint('      - تاريخ التسجيل: ${customer.createdAt}');
        }
      }
      
      if (craftsmen.isNotEmpty) {
        debugPrint('\n🔨 عينة من بيانات الحرفيين:');
        for (int i = 0; i < craftsmen.length && i < 3; i++) {
          final craftsman = craftsmen[i];
          debugPrint('   ${i + 1}. ${craftsman.name}');
          debugPrint('      - البريد: ${craftsman.email}');
          debugPrint('      - الهاتف: ${craftsman.phone}');
          debugPrint('      - الحرفة: ${craftsman.craftId}');
          debugPrint('      - التقييم: ${craftsman.rating}');
          debugPrint('      - الطلبات المكتملة: ${craftsman.completedOrders}');
          debugPrint('      - نشط: ${craftsman.isActive}');
        }
      }
      
      debugPrint('\n✅ انتهى تشخيص بيانات المستخدمين');
      
    } catch (e) {
      debugPrint('❌ خطأ في تشخيص بيانات المستخدمين: $e');
    }
  }
  
  /// إنشاء بيانات تجريبية للاختبار
  static Future<void> createTestData() async {
    debugPrint('🧪 إنشاء بيانات تجريبية...');
    
    try {
      final authService = AuthService();
      
      // إنشاء عميل تجريبي
      debugPrint('👤 إنشاء عميل تجريبي...');
      final customerResult = await authService.registerCustomer(
        name: 'أحمد محمد التجريبي',
        email: '<EMAIL>',
        phone: '0612345678',
        password: '123456',
        regionId: '3', // الرباط سلا القنيطرة
        cityId: '7',   // الرباط
      );
      
      if (customerResult.isSuccess) {
        debugPrint('✅ تم إنشاء العميل التجريبي بنجاح');
        debugPrint('   - المعرف: ${customerResult.userId}');
        debugPrint('   - الاسم: ${customerResult.name}');
        debugPrint('   - البريد: ${customerResult.email}');
      } else {
        debugPrint('❌ فشل في إنشاء العميل التجريبي: ${customerResult.errorMessage}');
      }
      
      // إنشاء حرفي تجريبي
      debugPrint('\n🔨 إنشاء حرفي تجريبي...');
      final craftsmanResult = await authService.registerCraftsman(
        name: 'محمد علي التجريبي',
        email: '<EMAIL>',
        phone: '0612345679',
        password: '123456',
        craftId: '1',  // كهربائي
        regionId: '3', // الرباط سلا القنيطرة
        cityId: '7',   // الرباط
      );
      
      if (craftsmanResult.isSuccess) {
        debugPrint('✅ تم إنشاء الحرفي التجريبي بنجاح');
        debugPrint('   - المعرف: ${craftsmanResult.userId}');
        debugPrint('   - الاسم: ${craftsmanResult.name}');
        debugPrint('   - البريد: ${craftsmanResult.email}');
      } else {
        debugPrint('❌ فشل في إنشاء الحرفي التجريبي: ${craftsmanResult.errorMessage}');
      }
      
      debugPrint('\n✅ انتهى إنشاء البيانات التجريبية');
      
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء البيانات التجريبية: $e');
    }
  }
}

/// شاشة تشخيص بيانات المستخدمين
class UserDataDebugScreen extends StatefulWidget {
  const UserDataDebugScreen({super.key});

  @override
  State<UserDataDebugScreen> createState() => _UserDataDebugScreenState();
}

class _UserDataDebugScreenState extends State<UserDataDebugScreen> {
  bool _isDebugging = false;
  bool _isCreatingTestData = false;
  String _debugOutput = '';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تشخيص بيانات المستخدمين'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            const Text(
              'أدوات تشخيص بيانات المستخدمين',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            
            ElevatedButton.icon(
              onPressed: _isDebugging ? null : _debugUserData,
              icon: _isDebugging 
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Icon(Icons.search),
              label: Text(_isDebugging ? 'جاري التشخيص...' : 'تشخيص بيانات المستخدمين'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
            ),
            
            const SizedBox(height: 16),
            
            ElevatedButton.icon(
              onPressed: _isCreatingTestData ? null : _createTestData,
              icon: _isCreatingTestData 
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Icon(Icons.add),
              label: Text(_isCreatingTestData ? 'جاري الإنشاء...' : 'إنشاء بيانات تجريبية'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
            ),
            
            const SizedBox(height: 24),
            
            const Text(
              'نتائج التشخيص:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: 8),
            
            Expanded(
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey[300]!),
                ),
                child: SingleChildScrollView(
                  child: Text(
                    _debugOutput.isEmpty 
                      ? 'اضغط على "تشخيص بيانات المستخدمين" لبدء التشخيص'
                      : _debugOutput,
                    style: const TextStyle(
                      fontFamily: 'monospace',
                      fontSize: 12,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _debugUserData() async {
    setState(() {
      _isDebugging = true;
      _debugOutput = 'جاري تشخيص بيانات المستخدمين...\n';
    });

    // تشغيل التشخيص وجمع النتائج
    await UserDataDebugger.debugUserData();
    
    setState(() {
      _isDebugging = false;
      _debugOutput = 'تم الانتهاء من التشخيص. تحقق من وحدة التحكم (Console) لرؤية النتائج التفصيلية.';
    });
  }

  Future<void> _createTestData() async {
    setState(() {
      _isCreatingTestData = true;
      _debugOutput = 'جاري إنشاء بيانات تجريبية...\n';
    });

    await UserDataDebugger.createTestData();
    
    setState(() {
      _isCreatingTestData = false;
      _debugOutput += '\nتم الانتهاء من إنشاء البيانات التجريبية. تحقق من وحدة التحكم (Console) لرؤية النتائج.';
    });
  }
}
