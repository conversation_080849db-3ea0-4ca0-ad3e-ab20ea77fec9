import '../database/database_helper.dart';
import '../models/craftsman_model.dart';

/// مستودع الحرفيين
class CraftsmanRepository {
  final DatabaseHelper _dbHelper = DatabaseHelper();

  /// إضافة حرفي جديد
  Future<int?> insertCraftsman(CraftsmanModel craftsman) async {
    try {
      final db = await _dbHelper.database;
      final id = await db.insert('craftsmen', craftsman.toMap());
      return id;
    } catch (e) {
      print('خطأ في إضافة الحرفي: $e');
      return null;
    }
  }

  /// الحصول على حرفي بالبريد الإلكتروني
  Future<CraftsmanModel?> getCraftsmanByEmail(String email) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'craftsmen',
        where: 'email = ?',
        whereArgs: [email],
        limit: 1,
      );

      if (maps.isEmpty) return null;
      return CraftsmanModel.fromMap(maps.first);
    } catch (e) {
      print('خطأ في الحصول على الحرفي بالبريد الإلكتروني: $e');
      return null;
    }
  }

  /// الحصول على حرفي بالمعرف
  Future<CraftsmanModel?> getCraftsmanById(int id) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'craftsmen',
        where: 'id = ?',
        whereArgs: [id],
        limit: 1,
      );

      if (maps.isEmpty) return null;
      return CraftsmanModel.fromMap(maps.first);
    } catch (e) {
      print('خطأ في الحصول على الحرفي: $e');
      return null;
    }
  }

  /// الحصول على جميع الحرفيين
  Future<List<CraftsmanModel>> getAllCraftsmen({bool activeOnly = true}) async {
    try {
      final db = await _dbHelper.database;
      String whereClause = '';
      List<dynamic> whereArgs = [];

      if (activeOnly) {
        whereClause = 'is_active = ?';
        whereArgs = [1];
      }

      final List<Map<String, dynamic>> maps = await db.query(
        'craftsmen',
        where: whereClause.isEmpty ? null : whereClause,
        whereArgs: whereArgs.isEmpty ? null : whereArgs,
        orderBy: 'rating DESC, completed_orders DESC',
      );

      return maps.map((map) => CraftsmanModel.fromMap(map)).toList();
    } catch (e) {
      print('خطأ في الحصول على الحرفيين: $e');
      return [];
    }
  }

  /// البحث في الحرفيين
  Future<List<CraftsmanModel>> searchCraftsmen(String query) async {
    try {
      final db = await _dbHelper.database;
      final searchQuery = '%$query%';

      final List<Map<String, dynamic>> maps = await db.query(
        'craftsmen',
        where:
            'is_active = 1 AND (name LIKE ? OR email LIKE ? OR phone LIKE ?)',
        whereArgs: [searchQuery, searchQuery, searchQuery],
        orderBy: 'rating DESC, name ASC',
      );

      return maps.map((map) => CraftsmanModel.fromMap(map)).toList();
    } catch (e) {
      print('خطأ في البحث في الحرفيين: $e');
      return [];
    }
  }

  /// الحصول على الحرفيين حسب الحرفة
  Future<List<CraftsmanModel>> getCraftsmenByCraft(String craftId) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'craftsmen',
        where: 'craft_id = ? AND is_active = 1',
        whereArgs: [craftId],
        orderBy: 'rating DESC, completed_orders DESC',
      );

      return maps.map((map) => CraftsmanModel.fromMap(map)).toList();
    } catch (e) {
      print('خطأ في الحصول على الحرفيين حسب الحرفة: $e');
      return [];
    }
  }

  /// الحصول على الحرفيين حسب الجهة
  Future<List<CraftsmanModel>> getCraftsmenByRegion(String regionId) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'craftsmen',
        where: 'region_id = ? AND is_active = 1',
        whereArgs: [regionId],
        orderBy: 'rating DESC, completed_orders DESC',
      );

      return maps.map((map) => CraftsmanModel.fromMap(map)).toList();
    } catch (e) {
      print('خطأ في الحصول على الحرفيين حسب الجهة: $e');
      return [];
    }
  }

  /// الحصول على الحرفيين حسب المدينة
  Future<List<CraftsmanModel>> getCraftsmenByCity(String cityId) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'craftsmen',
        where: 'city_id = ? AND is_active = 1',
        whereArgs: [cityId],
        orderBy: 'rating DESC, completed_orders DESC',
      );

      return maps.map((map) => CraftsmanModel.fromMap(map)).toList();
    } catch (e) {
      print('خطأ في الحصول على الحرفيين حسب المدينة: $e');
      return [];
    }
  }

  /// الحصول على الحرفيين المميزين
  Future<List<CraftsmanModel>> getFeaturedCraftsmen({int limit = 10}) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'craftsmen',
        where: 'is_active = 1 AND rating >= 4.0 AND completed_orders >= 10',
        orderBy: 'rating DESC, completed_orders DESC',
        limit: limit,
      );

      return maps.map((map) => CraftsmanModel.fromMap(map)).toList();
    } catch (e) {
      print('خطأ في الحصول على الحرفيين المميزين: $e');
      return [];
    }
  }

  /// البحث المتقدم في الحرفيين
  Future<List<CraftsmanModel>> advancedSearchCraftsmen({
    String? query,
    String? craftId,
    String? regionId,
    String? cityId,
    double? minRating,
    int? minCompletedOrders,
  }) async {
    try {
      final db = await _dbHelper.database;

      String whereClause = 'is_active = 1';
      List<dynamic> whereArgs = [];

      if (query != null && query.isNotEmpty) {
        whereClause += ' AND (name LIKE ? OR email LIKE ?)';
        final searchQuery = '%$query%';
        whereArgs.addAll([searchQuery, searchQuery]);
      }

      if (craftId != null) {
        whereClause += ' AND craft_id = ?';
        whereArgs.add(craftId);
      }

      if (regionId != null) {
        whereClause += ' AND region_id = ?';
        whereArgs.add(regionId);
      }

      if (cityId != null) {
        whereClause += ' AND city_id = ?';
        whereArgs.add(cityId);
      }

      if (minRating != null) {
        whereClause += ' AND rating >= ?';
        whereArgs.add(minRating);
      }

      if (minCompletedOrders != null) {
        whereClause += ' AND completed_orders >= ?';
        whereArgs.add(minCompletedOrders);
      }

      final List<Map<String, dynamic>> maps = await db.query(
        'craftsmen',
        where: whereClause,
        whereArgs: whereArgs,
        orderBy: 'rating DESC, completed_orders DESC',
      );

      return maps.map((map) => CraftsmanModel.fromMap(map)).toList();
    } catch (e) {
      print('خطأ في البحث المتقدم في الحرفيين: $e');
      return [];
    }
  }

  /// تحديث حرفي
  Future<bool> updateCraftsman(CraftsmanModel craftsman) async {
    try {
      final db = await _dbHelper.database;
      final updatedCraftsman = craftsman.copyWith(
        updatedAt: DateTime.now(),
      );

      final result = await db.update(
        'craftsmen',
        updatedCraftsman.toMap(),
        where: 'id = ?',
        whereArgs: [craftsman.id],
      );

      return result > 0;
    } catch (e) {
      print('خطأ في تحديث الحرفي: $e');
      return false;
    }
  }

  /// تحديث تقييم الحرفي
  Future<bool> updateCraftsmanRating(int craftsmanId, double newRating) async {
    try {
      final db = await _dbHelper.database;

      // الحصول على التقييم الحالي وعدد الطلبات
      final currentData = await db.query(
        'craftsmen',
        columns: ['rating', 'completed_orders'],
        where: 'id = ?',
        whereArgs: [craftsmanId],
        limit: 1,
      );

      if (currentData.isEmpty) return false;

      final currentRating = (currentData.first['rating'] as double?) ?? 0.0;
      final completedOrders =
          (currentData.first['completed_orders'] as int?) ?? 0;

      // حساب التقييم الجديد (متوسط مرجح)
      double updatedRating;
      if (completedOrders == 0) {
        updatedRating = newRating;
      } else {
        updatedRating = ((currentRating * completedOrders) + newRating) /
            (completedOrders + 1);
      }

      // تحديث التقييم وزيادة عدد الطلبات المكتملة
      final result = await db.update(
        'craftsmen',
        {
          'rating': updatedRating,
          'completed_orders': completedOrders + 1,
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [craftsmanId],
      );

      return result > 0;
    } catch (e) {
      print('خطأ في تحديث تقييم الحرفي: $e');
      return false;
    }
  }

  /// تفعيل/إلغاء تفعيل حرفي
  Future<bool> toggleCraftsmanStatus(int craftsmanId, bool isActive) async {
    try {
      final db = await _dbHelper.database;
      final result = await db.update(
        'craftsmen',
        {
          'is_active': isActive ? 1 : 0,
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [craftsmanId],
      );

      return result > 0;
    } catch (e) {
      print('خطأ في تغيير حالة الحرفي: $e');
      return false;
    }
  }

  /// حذف حرفي
  Future<bool> deleteCraftsman(int id) async {
    try {
      final db = await _dbHelper.database;
      final result = await db.delete(
        'craftsmen',
        where: 'id = ?',
        whereArgs: [id],
      );

      return result > 0;
    } catch (e) {
      print('خطأ في حذف الحرفي: $e');
      return false;
    }
  }

  /// التحقق من وجود بريد إلكتروني
  Future<bool> emailExists(String email, {int? excludeId}) async {
    try {
      final db = await _dbHelper.database;
      String whereClause = 'email = ?';
      List<dynamic> whereArgs = [email];

      if (excludeId != null) {
        whereClause += ' AND id != ?';
        whereArgs.add(excludeId);
      }

      final List<Map<String, dynamic>> maps = await db.query(
        'craftsmen',
        where: whereClause,
        whereArgs: whereArgs,
        limit: 1,
      );

      return maps.isNotEmpty;
    } catch (e) {
      print('خطأ في التحقق من البريد الإلكتروني: $e');
      return false;
    }
  }

  /// الحصول على إحصائيات الحرفيين
  Future<Map<String, dynamic>> getCraftsmenStatistics() async {
    try {
      final db = await _dbHelper.database;

      // إجمالي الحرفيين
      final totalResult =
          await db.rawQuery('SELECT COUNT(*) as count FROM craftsmen');
      final totalCraftsmen = totalResult.first['count'] as int;

      // الحرفيين النشطين
      final activeResult = await db.rawQuery(
          'SELECT COUNT(*) as count FROM craftsmen WHERE is_active = 1');
      final activeCraftsmen = activeResult.first['count'] as int;

      // الحرفيين الجدد هذا الشهر
      final now = DateTime.now();
      final startOfMonth = DateTime(now.year, now.month, 1);
      final newThisMonthResult = await db.rawQuery(
        'SELECT COUNT(*) as count FROM craftsmen WHERE created_at >= ?',
        [startOfMonth.toIso8601String()],
      );
      final newThisMonth = newThisMonthResult.first['count'] as int;

      // الحرفيين حسب الحرفة
      final craftStatsResult = await db.rawQuery('''
        SELECT c.name_ar as craft_name, COUNT(cm.id) as craftsmen_count
        FROM crafts c
        LEFT JOIN craftsmen cm ON c.id = cm.craft_id AND cm.is_active = 1
        GROUP BY c.id, c.name_ar
        ORDER BY craftsmen_count DESC
        LIMIT 10
      ''');

      // متوسط التقييم
      final avgRatingResult = await db.rawQuery(
          'SELECT AVG(rating) as avg_rating FROM craftsmen WHERE is_active = 1');
      final avgRating = (avgRatingResult.first['avg_rating'] as double?) ?? 0.0;

      return {
        'total_craftsmen': totalCraftsmen,
        'active_craftsmen': activeCraftsmen,
        'new_this_month': newThisMonth,
        'average_rating': avgRating,
        'craftsmen_by_craft': craftStatsResult,
      };
    } catch (e) {
      print('خطأ في الحصول على إحصائيات الحرفيين: $e');
      return {};
    }
  }
}
