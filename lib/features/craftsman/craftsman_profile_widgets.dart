import 'package:flutter/material.dart';
import '../../shared/themes/app_theme.dart';
import '../../core/models/craftsman_model.dart';
import '../../core/models/region_model.dart';
import '../../core/models/city_model.dart';
import '../../core/models/craft_model.dart';
import '../../shared/widgets/region_city_selector.dart';
import '../../shared/widgets/craft_selector.dart';

/// ويدجت رأس الملف الشخصي للحرفي
Widget buildProfileHeader(CraftsmanModel? craftsman) {
  return Container(
    padding: const EdgeInsets.all(20),
    decoration: BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(12),
      boxShadow: [
        BoxShadow(
          color: Colors.grey.withOpacity(0.1),
          spreadRadius: 1,
          blurRadius: 6,
          offset: const Offset(0, 3),
        ),
      ],
    ),
    child: Row(
      children: [
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            color: AppTheme.primaryColor.withOpacity(0.1),
            borderRadius: BorderRadius.circular(40),
          ),
          child: Icon(
            Icons.person,
            size: 40,
            color: AppTheme.primaryColor,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                craftsman?.name ?? 'غير محدد',
                style: const TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                craftsman?.email ?? 'غير محدد',
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(
                    Icons.star,
                    size: 16,
                    color: Colors.amber,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${craftsman?.rating?.toStringAsFixed(1) ?? '0.0'}',
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    '(${craftsman?.reviewsCount ?? 0} تقييم)',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    ),
  );
}

/// ويدجت قسم المعلومات الشخصية
Widget buildPersonalInfoSection({
  required TextEditingController nameController,
  required TextEditingController phoneController,
  required TextEditingController emailController,
  required String? Function(String?) validateMoroccanPhone,
}) {
  return Container(
    padding: const EdgeInsets.all(20),
    decoration: BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(12),
      boxShadow: [
        BoxShadow(
          color: Colors.grey.withOpacity(0.1),
          spreadRadius: 1,
          blurRadius: 6,
          offset: const Offset(0, 3),
        ),
      ],
    ),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'المعلومات الشخصية',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: nameController,
          decoration: const InputDecoration(
            labelText: 'الاسم الكامل',
            prefixIcon: Icon(Icons.person),
            border: OutlineInputBorder(),
          ),
          validator: (value) {
            if (value == null || value.trim().isEmpty) {
              return 'الاسم مطلوب';
            }
            if (value.trim().length < 2) {
              return 'الاسم يجب أن يكون أكثر من حرفين';
            }
            return null;
          },
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: phoneController,
          decoration: const InputDecoration(
            labelText: 'رقم الهاتف',
            prefixIcon: Icon(Icons.phone),
            border: OutlineInputBorder(),
            hintText: '+212612345678',
          ),
          keyboardType: TextInputType.phone,
          validator: validateMoroccanPhone,
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: emailController,
          decoration: const InputDecoration(
            labelText: 'البريد الإلكتروني',
            prefixIcon: Icon(Icons.email),
            border: OutlineInputBorder(),
            suffixIcon: Icon(Icons.lock, color: Colors.grey),
          ),
          enabled: false, // للقراءة فقط
          style: TextStyle(color: Colors.grey[600]),
        ),
      ],
    ),
  );
}

/// ويدجت قسم الموقع
Widget buildLocationSection({
  required List<RegionModel> regions,
  required List<CityModel> cities,
  required String? selectedRegionId,
  required String? selectedCityId,
  required Function(String?) onRegionChanged,
  required Function(String?) onCityChanged,
}) {
  return Container(
    padding: const EdgeInsets.all(20),
    decoration: BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(12),
      boxShadow: [
        BoxShadow(
          color: Colors.grey.withOpacity(0.1),
          spreadRadius: 1,
          blurRadius: 6,
          offset: const Offset(0, 3),
        ),
      ],
    ),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'الموقع',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        RegionCitySelector(
          regions: regions,
          cities: cities,
          selectedRegionId: selectedRegionId,
          selectedCityId: selectedCityId,
          onRegionChanged: onRegionChanged,
          onCityChanged: onCityChanged,
        ),
      ],
    ),
  );
}

/// ويدجت قسم الحرفة
Widget buildCraftSection({
  required List<CraftModel> crafts,
  required String? selectedCraftId,
  required Function(String?) onCraftChanged,
}) {
  return Container(
    padding: const EdgeInsets.all(20),
    decoration: BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(12),
      boxShadow: [
        BoxShadow(
          color: Colors.grey.withOpacity(0.1),
          spreadRadius: 1,
          blurRadius: 6,
          offset: const Offset(0, 3),
        ),
      ],
    ),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'الحرفة',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        CraftSelector(
          crafts: crafts,
          selectedCraftId: selectedCraftId,
          onCraftChanged: onCraftChanged,
        ),
      ],
    ),
  );
}

/// ويدجت قسم الوصف
Widget buildDescriptionSection({
  required TextEditingController descriptionController,
}) {
  return Container(
    padding: const EdgeInsets.all(20),
    decoration: BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(12),
      boxShadow: [
        BoxShadow(
          color: Colors.grey.withOpacity(0.1),
          spreadRadius: 1,
          blurRadius: 6,
          offset: const Offset(0, 3),
        ),
      ],
    ),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'الوصف',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        TextFormField(
          controller: descriptionController,
          decoration: const InputDecoration(
            labelText: 'وصف الخدمات والخبرات',
            prefixIcon: Icon(Icons.description),
            border: OutlineInputBorder(),
            hintText: 'اكتب وصفاً مختصراً عن خدماتك وخبراتك...',
          ),
          maxLines: 4,
          maxLength: 500,
          validator: (value) {
            if (value != null && value.length > 500) {
              return 'الوصف يجب أن يكون أقل من 500 حرف';
            }
            return null;
          },
        ),
      ],
    ),
  );
}

/// ويدجت زر الحفظ
Widget buildSaveButton({
  required bool isSaving,
  required VoidCallback onSave,
}) {
  return SizedBox(
    width: double.infinity,
    height: 50,
    child: ElevatedButton(
      onPressed: isSaving ? null : onSave,
      style: ElevatedButton.styleFrom(
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      child: isSaving
          ? const Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                ),
                SizedBox(width: 12),
                Text('جاري الحفظ...'),
              ],
            )
          : const Text(
              'حفظ التغييرات',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
    ),
  );
}
