import 'package:flutter/foundation.dart';

/// مسجل الأحداث للتطبيق
class AppLogger {
  static const String _tag = 'ElmokefApp';

  /// تسجيل رسالة معلومات
  static void info(String message, [dynamic error]) {
    if (kDebugMode) {
      print('[$_tag] INFO: $message');
      if (error != null) {
        print('[$_tag] ERROR DETAILS: $error');
      }
    }
  }

  /// تسجيل رسالة خطأ
  static void error(String message, [dynamic error]) {
    if (kDebugMode) {
      print('[$_tag] ERROR: $message');
      if (error != null) {
        print('[$_tag] ERROR DETAILS: $error');
      }
    }
  }

  /// تسجيل رسالة تحذير
  static void warning(String message, [dynamic error]) {
    if (kDebugMode) {
      print('[$_tag] WARNING: $message');
      if (error != null) {
        print('[$_tag] WARNING DETAILS: $error');
      }
    }
  }

  /// تسجيل رسالة تصحيح
  static void debug(String message, [dynamic error]) {
    if (kDebugMode) {
      print('[$_tag] DEBUG: $message');
      if (error != null) {
        print('[$_tag] DEBUG DETAILS: $error');
      }
    }
  }

  /// تسجيل بداية عملية
  static void startOperation(String operation) {
    if (kDebugMode) {
      print('[$_tag] START: $operation');
    }
  }

  /// تسجيل انتهاء عملية
  static void endOperation(String operation, [Duration? duration]) {
    if (kDebugMode) {
      final durationText = duration != null ? ' (${duration.inMilliseconds}ms)' : '';
      print('[$_tag] END: $operation$durationText');
    }
  }

  /// تسجيل استعلام قاعدة البيانات
  static void dbQuery(String query, [List<dynamic>? args]) {
    if (kDebugMode) {
      print('[$_tag] DB QUERY: $query');
      if (args != null && args.isNotEmpty) {
        print('[$_tag] DB ARGS: $args');
      }
    }
  }

  /// تسجيل نتيجة استعلام قاعدة البيانات
  static void dbResult(String operation, int count) {
    if (kDebugMode) {
      print('[$_tag] DB RESULT: $operation returned $count rows');
    }
  }
}
