/// نموذج الجهة المغربية
class RegionModel {
  final String id;
  final String nameAr;
  final String nameEn;
  final String nameFr;
  final String createdAt;
  final List<CityModel> cities;

  RegionModel({
    required this.id,
    required this.nameAr,
    required this.nameEn,
    required this.nameFr,
    required this.createdAt,
    this.cities = const [],
  });

  factory RegionModel.fromMap(Map<String, dynamic> map) {
    return RegionModel(
      id: map['id'] ?? '',
      nameAr: map['name_ar'] ?? '',
      nameEn: map['name_en'] ?? '',
      nameFr: map['name_fr'] ?? '',
      createdAt: map['created_at'] ?? '',
      cities: [], // سيتم تحميلها منفصلة
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name_ar': nameAr,
      'name_en': nameEn,
      'name_fr': nameFr,
      'created_at': createdAt,
    };
  }

  RegionModel copyWith({
    String? id,
    String? nameAr,
    String? nameEn,
    String? nameFr,
    String? createdAt,
    List<CityModel>? cities,
  }) {
    return RegionModel(
      id: id ?? this.id,
      nameAr: nameAr ?? this.nameAr,
      nameEn: nameEn ?? this.nameEn,
      nameFr: nameFr ?? this.nameFr,
      createdAt: createdAt ?? this.createdAt,
      cities: cities ?? this.cities,
    );
  }

  @override
  String toString() {
    return 'RegionModel(id: $id, nameAr: $nameAr, nameEn: $nameEn, nameFr: $nameFr, cities: ${cities.length})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is RegionModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// نموذج المدينة المغربية
class CityModel {
  final int? id;
  final String regionId;
  final String nameAr;
  final String nameEn;
  final String nameFr;
  final double? latitude;
  final double? longitude;
  final int population;
  final bool isActive;
  final String createdAt;

  CityModel({
    this.id,
    required this.regionId,
    required this.nameAr,
    required this.nameEn,
    required this.nameFr,
    this.latitude,
    this.longitude,
    this.population = 0,
    this.isActive = true,
    required this.createdAt,
  });

  factory CityModel.fromMap(Map<String, dynamic> map) {
    return CityModel(
      id: map['id']?.toInt(),
      regionId: map['region_id'] ?? '',
      nameAr: map['name_ar'] ?? '',
      nameEn: map['name_en'] ?? '',
      nameFr: map['name_fr'] ?? '',
      latitude: map['latitude']?.toDouble(),
      longitude: map['longitude']?.toDouble(),
      population: map['population']?.toInt() ?? 0,
      isActive: (map['is_active'] ?? 1) == 1,
      createdAt: map['created_at'] ?? '',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'region_id': regionId,
      'name_ar': nameAr,
      'name_en': nameEn,
      'name_fr': nameFr,
      'latitude': latitude,
      'longitude': longitude,
      'population': population,
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt,
    };
  }

  CityModel copyWith({
    int? id,
    String? regionId,
    String? nameAr,
    String? nameEn,
    String? nameFr,
    double? latitude,
    double? longitude,
    int? population,
    bool? isActive,
    String? createdAt,
  }) {
    return CityModel(
      id: id ?? this.id,
      regionId: regionId ?? this.regionId,
      nameAr: nameAr ?? this.nameAr,
      nameEn: nameEn ?? this.nameEn,
      nameFr: nameFr ?? this.nameFr,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      population: population ?? this.population,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  String toString() {
    return 'CityModel(id: $id, regionId: $regionId, nameAr: $nameAr, nameEn: $nameEn, nameFr: $nameFr, population: $population)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CityModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// إحصائيات قاعدة البيانات
class DatabaseStats {
  final int regionsCount;
  final int citiesCount;
  final int craftsCount;
  final int categoriesCount;
  final int craftsmenCount;
  final int customersCount;

  DatabaseStats({
    required this.regionsCount,
    required this.citiesCount,
    required this.craftsCount,
    required this.categoriesCount,
    required this.craftsmenCount,
    required this.customersCount,
  });

  factory DatabaseStats.fromMap(Map<String, dynamic> map) {
    return DatabaseStats(
      regionsCount: map['regions_count']?.toInt() ?? 0,
      citiesCount: map['cities_count']?.toInt() ?? 0,
      craftsCount: map['crafts_count']?.toInt() ?? 0,
      categoriesCount: map['categories_count']?.toInt() ?? 0,
      craftsmenCount: map['craftsmen_count']?.toInt() ?? 0,
      customersCount: map['customers_count']?.toInt() ?? 0,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'regions_count': regionsCount,
      'cities_count': citiesCount,
      'crafts_count': craftsCount,
      'categories_count': categoriesCount,
      'craftsmen_count': craftsmenCount,
      'customers_count': customersCount,
    };
  }

  @override
  String toString() {
    return 'DatabaseStats(regions: $regionsCount, cities: $citiesCount, crafts: $craftsCount, categories: $categoriesCount, craftsmen: $craftsmenCount, customers: $customersCount)';
  }
}

/// نتائج البحث المتقدم
class SearchResult<T> {
  final List<T> results;
  final int totalCount;
  final String query;
  final Map<String, dynamic> filters;

  SearchResult({
    required this.results,
    required this.totalCount,
    required this.query,
    this.filters = const {},
  });

  bool get hasResults => results.isNotEmpty;
  bool get isEmpty => results.isEmpty;

  @override
  String toString() {
    return 'SearchResult(query: $query, totalCount: $totalCount, resultsCount: ${results.length})';
  }
}
