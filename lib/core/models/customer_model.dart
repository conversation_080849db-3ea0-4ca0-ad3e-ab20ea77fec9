/// نموذج العميل
class CustomerModel {
  final int? id;
  final String name;
  final String email;
  final String phone;
  final String regionId;
  final String cityId;
  final DateTime createdAt;
  final DateTime? updatedAt;

  CustomerModel({
    this.id,
    required this.name,
    required this.email,
    required this.phone,
    required this.regionId,
    required this.cityId,
    required this.createdAt,
    this.updatedAt,
  });

  /// إنشاء نموذج من Map
  factory CustomerModel.fromMap(Map<String, dynamic> map) {
    return CustomerModel(
      id: map['id']?.toInt(),
      name: map['name'] ?? '',
      email: map['email'] ?? '',
      phone: map['phone'] ?? '',
      regionId: map['region_id'] ?? '',
      cityId: map['city_id'] ?? '',
      createdAt: DateTime.parse(map['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
    );
  }

  /// تحويل النموذج إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phone': phone,
      'region_id': regionId,
      'city_id': cityId,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  /// إنشاء نسخة محدثة من النموذج
  CustomerModel copyWith({
    int? id,
    String? name,
    String? email,
    String? phone,
    String? regionId,
    String? cityId,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CustomerModel(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      regionId: regionId ?? this.regionId,
      cityId: cityId ?? this.cityId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'CustomerModel(id: $id, name: $name, email: $email, phone: $phone, regionId: $regionId, cityId: $cityId)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CustomerModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  /// التحقق من صحة البيانات
  bool get isValid {
    return name.isNotEmpty &&
        email.isNotEmpty &&
        phone.isNotEmpty &&
        regionId.isNotEmpty &&
        cityId.isNotEmpty &&
        _isValidEmail(email) &&
        _isValidPhone(phone);
  }

  /// التحقق من صحة البريد الإلكتروني
  bool _isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  /// التحقق من صحة رقم الهاتف
  bool _isValidPhone(String phone) {
    return RegExp(r'^\+?[0-9]{10,15}$').hasMatch(phone.replaceAll(' ', ''));
  }

  /// الحصول على اسم مختصر
  String get shortName {
    final parts = name.split(' ');
    if (parts.length >= 2) {
      return '${parts[0]} ${parts[1]}';
    }
    return name;
  }

  /// الحصول على الأحرف الأولى من الاسم
  String get initials {
    final parts = name.split(' ');
    if (parts.length >= 2) {
      return '${parts[0][0]}${parts[1][0]}'.toUpperCase();
    } else if (parts.isNotEmpty) {
      return parts[0][0].toUpperCase();
    }
    return 'ع'; // عميل
  }
}
