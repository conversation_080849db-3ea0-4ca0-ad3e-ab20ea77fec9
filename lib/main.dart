import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'dart:io';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'features/splash/splash_screen.dart';
import 'features/test/enhanced_home_test.dart';

/// نقطة دخول التطبيق
void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  try {
    // تهيئة قاعدة البيانات للمنصات المكتبية
    if (Platform.isLinux || Platform.isWindows || Platform.isMacOS) {
      sqfliteFfiInit();
      databaseFactory = databaseFactoryFfi;
    }

    // تعيين اتجاه الشاشة
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
  } catch (e) {
    debugPrint('❌ خطأ في تهيئة التطبيق: $e');
  }

  // تشغيل التطبيق
  runApp(const ElmoqefApp());
}

/// التطبيق الرئيسي - الموقف
class ElmoqefApp extends StatelessWidget {
  const ElmoqefApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      // معلومات التطبيق
      title: 'الموقف',
      debugShowCheckedModeBanner: false,

      // الثيم والألوان
      theme: ThemeData(
        primarySwatch: Colors.blue,
        fontFamily: 'Cairo',
        textTheme: const TextTheme(
          bodyLarge: TextStyle(fontSize: 16),
          bodyMedium: TextStyle(fontSize: 14),
        ),
      ),

      // اللغة والمنطقة
      locale: const Locale('ar'),
      supportedLocales: const [
        Locale('ar'),
        Locale('en'),
        Locale('fr'),
      ],
      localizationsDelegates: const [
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],

      // الصفحة الرئيسية - اختبار الصفحات المحسنة
      home: const EnhancedHomeTestScreen(),

      // إعدادات إضافية
      builder: (context, child) {
        return MediaQuery(
          data: MediaQuery.of(context).copyWith(
            textScaler: const TextScaler.linear(1.0),
          ),
          child: child!,
        );
      },
    );
  }
}
