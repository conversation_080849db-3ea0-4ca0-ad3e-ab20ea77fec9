import 'dart:async';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'package:path/path.dart';
import 'package:flutter/foundation.dart';

/// مساعد قاعدة البيانات المحدث مع المدن الشاملة
class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  factory DatabaseHelper() => _instance;
  DatabaseHelper._internal();

  static Database? _database;
  static const String _databaseName = 'elmokef_complete.db';
  static const int _databaseVersion = 2;

  /// الحصول على قاعدة البيانات
  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  /// تهيئة قاعدة البيانات
  Future<Database> _initDatabase() async {
    try {
      // تهيئة sqflite_ffi للمنصات المكتبية
      if (!kIsWeb &&
          (defaultTargetPlatform == TargetPlatform.linux ||
              defaultTargetPlatform == TargetPlatform.windows ||
              defaultTargetPlatform == TargetPlatform.macOS)) {
        sqfliteFfiInit();
        databaseFactory = databaseFactoryFfi;
      }

      final databasesPath = await getDatabasesPath();
      final path = join(databasesPath, _databaseName);

      debugPrint('🗄️ تهيئة قاعدة البيانات: $path');

      return await openDatabase(
        path,
        version: _databaseVersion,
        onCreate: _onCreate,
        onUpgrade: _onUpgrade,
      );
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة قاعدة البيانات: $e');
      rethrow;
    }
  }

  /// إنشاء قاعدة البيانات
  Future<void> _onCreate(Database db, int version) async {
    debugPrint('🏗️ إنشاء قاعدة البيانات الإصدار $version');

    await _createTables(db);
    await _insertInitialData(db);

    debugPrint('✅ تم إنشاء قاعدة البيانات بنجاح');
  }

  /// ترقية قاعدة البيانات
  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    debugPrint('🔄 ترقية قاعدة البيانات من $oldVersion إلى $newVersion');

    if (oldVersion < 2) {
      // حذف الجداول القديمة وإعادة إنشائها
      await db.execute('DROP TABLE IF EXISTS cities');
      await db.execute('DROP TABLE IF EXISTS regions');
      await db.execute('DROP TABLE IF EXISTS crafts');
      await db.execute('DROP TABLE IF EXISTS customers');
      await db.execute('DROP TABLE IF EXISTS craftsmen');
      await db.execute('DROP TABLE IF EXISTS orders');
      await db.execute('DROP TABLE IF EXISTS user_auth');

      await _createTables(db);
      await _insertInitialData(db);
    }

    debugPrint('✅ تم ترقية قاعدة البيانات بنجاح');
  }

  /// إنشاء الجداول
  Future<void> _createTables(Database db) async {
    debugPrint('📋 إنشاء الجداول...');

    // جدول الجهات
    await db.execute('''
      CREATE TABLE regions (
        id TEXT PRIMARY KEY,
        name_ar TEXT NOT NULL,
        name_fr TEXT NOT NULL,
        name_en TEXT NOT NULL,
        created_at TEXT NOT NULL
      )
    ''');

    // جدول المدن
    await db.execute('''
      CREATE TABLE cities (
        id TEXT PRIMARY KEY,
        region_id TEXT NOT NULL,
        name_ar TEXT NOT NULL,
        name_fr TEXT NOT NULL,
        name_en TEXT NOT NULL,
        created_at TEXT NOT NULL,
        FOREIGN KEY (region_id) REFERENCES regions (id)
      )
    ''');

    // جدول الحرف
    await db.execute('''
      CREATE TABLE crafts (
        id INTEGER PRIMARY KEY,
        name_ar TEXT NOT NULL,
        name_fr TEXT NOT NULL,
        name_en TEXT NOT NULL,
        created_at TEXT NOT NULL
      )
    ''');

    // جدول العملاء
    await db.execute('''
      CREATE TABLE customers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        email TEXT UNIQUE NOT NULL,
        phone TEXT NOT NULL,
        region_id TEXT NOT NULL,
        city_id TEXT NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        FOREIGN KEY (region_id) REFERENCES regions (id),
        FOREIGN KEY (city_id) REFERENCES cities (id)
      )
    ''');

    // جدول الحرفيين
    await db.execute('''
      CREATE TABLE craftsmen (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        email TEXT UNIQUE NOT NULL,
        phone TEXT NOT NULL,
        craft_id TEXT NOT NULL,
        region_id TEXT NOT NULL,
        city_id TEXT NOT NULL,
        rating REAL DEFAULT 0.0,
        completed_orders INTEGER DEFAULT 0,
        is_active INTEGER DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        FOREIGN KEY (craft_id) REFERENCES crafts (id),
        FOREIGN KEY (region_id) REFERENCES regions (id),
        FOREIGN KEY (city_id) REFERENCES cities (id)
      )
    ''');

    // جدول الطلبات
    await db.execute('''
      CREATE TABLE orders (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        customer_id INTEGER NOT NULL,
        craftsman_id INTEGER NOT NULL,
        craft_id TEXT NOT NULL,
        title TEXT NOT NULL,
        description TEXT NOT NULL,
        status TEXT NOT NULL DEFAULT 'pending',
        price REAL,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        FOREIGN KEY (customer_id) REFERENCES customers (id),
        FOREIGN KEY (craftsman_id) REFERENCES craftsmen (id),
        FOREIGN KEY (craft_id) REFERENCES crafts (id)
      )
    ''');

    // جدول المصادقة
    await db.execute('''
      CREATE TABLE user_auth (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        user_type TEXT NOT NULL,
        password_hash TEXT NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        UNIQUE(user_id, user_type)
      )
    ''');

    // جدول الإشعارات
    await db.execute('''
      CREATE TABLE notifications (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        message TEXT NOT NULL,
        type TEXT NOT NULL,
        is_read INTEGER NOT NULL DEFAULT 0,
        created_at TEXT NOT NULL,
        user_id TEXT NOT NULL,
        related_id TEXT,
        data TEXT
      )
    ''');

    // إنشاء الفهارس
    await _createIndexes(db);

    debugPrint('✅ تم إنشاء الجداول بنجاح');
  }

  /// إنشاء الفهارس
  Future<void> _createIndexes(Database db) async {
    debugPrint('🔍 إنشاء الفهارس...');

    await db.execute('CREATE INDEX idx_customers_email ON customers (email)');
    await db.execute(
        'CREATE INDEX idx_customers_region_city ON customers (region_id, city_id)');
    await db.execute('CREATE INDEX idx_craftsmen_email ON craftsmen (email)');
    await db
        .execute('CREATE INDEX idx_craftsmen_craft ON craftsmen (craft_id)');
    await db.execute(
        'CREATE INDEX idx_craftsmen_region_city ON craftsmen (region_id, city_id)');
    await db.execute('CREATE INDEX idx_craftsmen_rating ON craftsmen (rating)');
    await db.execute(
        'CREATE INDEX idx_user_auth_user ON user_auth (user_id, user_type)');
    await db.execute(
        'CREATE INDEX idx_notifications_user ON notifications (user_id)');
    await db.execute(
        'CREATE INDEX idx_notifications_read ON notifications (user_id, is_read)');
    await db.execute(
        'CREATE INDEX idx_notifications_type ON notifications (user_id, type)');
    await db.execute(
        'CREATE INDEX idx_notifications_date ON notifications (created_at)');

    debugPrint('✅ تم إنشاء الفهارس بنجاح');
  }

  /// إدراج البيانات الأولية
  Future<void> _insertInitialData(Database db) async {
    debugPrint('📝 إدراج البيانات الأولية...');

    // إدراج الجهات
    await _insertRegions(db);

    // إدراج المدن الشاملة (266 مدينة)
    await _insertCities(db);

    // إدراج الحرف
    await _insertCrafts(db);

    debugPrint('✅ تم إدراج البيانات الأولية بنجاح');
  }

  /// إدراج الجهات
  Future<void> _insertRegions(Database db) async {
    final regions = [
      {
        'id': '1',
        'name_ar': 'الداخلة وادي الذهب',
        'name_fr': 'Dakhla-Oued Ed-Dahab',
        'name_en': 'Dakhla-Oued Ed-Dahab'
      },
      {
        'id': '2',
        'name_ar': 'الدار البيضاء سطات',
        'name_fr': 'Casablanca-Settat',
        'name_en': 'Casablanca-Settat'
      },
      {
        'id': '3',
        'name_ar': 'الرباط سلا القنيطرة',
        'name_fr': 'Rabat-Salé-Kénitra',
        'name_en': 'Rabat-Sale-Kenitra'
      },
      {
        'id': '4',
        'name_ar': 'الشرق',
        'name_fr': 'Oriental',
        'name_en': 'Oriental'
      },
      {
        'id': '5',
        'name_ar': 'العيون الساقية الحمراء',
        'name_fr': 'Laâyoune-Sakia El Hamra',
        'name_en': 'Laayoune-Sakia El Hamra'
      },
      {
        'id': '6',
        'name_ar': 'بني ملال خنيفرة',
        'name_fr': 'Béni Mellal-Khénifra',
        'name_en': 'Beni Mellal-Khenifra'
      },
      {
        'id': '7',
        'name_ar': 'مراكش آسفي',
        'name_fr': 'Marrakech-Safi',
        'name_en': 'Marrakech-Safi'
      },
      {
        'id': '8',
        'name_ar': 'درعة تافيلالت',
        'name_fr': 'Drâa-Tafilalet',
        'name_en': 'Draa-Tafilalet'
      },
      {
        'id': '9',
        'name_ar': 'سوس ماسة',
        'name_fr': 'Souss-Massa',
        'name_en': 'Souss-Massa'
      },
      {
        'id': '10',
        'name_ar': 'كلميم واد نون',
        'name_fr': 'Guelmim-Oued Noun',
        'name_en': 'Guelmim-Oued Noun'
      },
      {
        'id': '11',
        'name_ar': 'فاس مكناس',
        'name_fr': 'Fès-Meknès',
        'name_en': 'Fez-Meknes'
      },
      {
        'id': '12',
        'name_ar': 'طنجة تطوان الحسيمة',
        'name_fr': 'Tanger-Tétouan-Al Hoceïma',
        'name_en': 'Tangier-Tetouan-Al Hoceima'
      },
    ];

    for (final region in regions) {
      await db.insert('regions', {
        ...region,
        'created_at': DateTime.now().toIso8601String(),
      });
    }
  }

  /// إدراج المدن الشاملة (266 مدينة)
  Future<void> _insertCities(Database db) async {
    // التحقق من وجود المدن مسبقاً
    final existingCities = await db.query('cities', limit: 1);
    if (existingCities.isNotEmpty) {
      debugPrint('🏙️ المدن موجودة مسبقاً، تخطي الإدراج');
      return;
    }

    debugPrint('🏙️ إدراج المدن المغربية...');

    // إدراج بيانات أساسية للمدن (يمكن تحديثها لاحقاً)
    final sampleCities = [
      {
        'id': '1',
        'region_id': '1',
        'name_ar': 'الدار البيضاء',
        'name_en': 'Casablanca',
        'name_fr': 'Casablanca'
      },
      {
        'id': '2',
        'region_id': '2',
        'name_ar': 'الرباط',
        'name_en': 'Rabat',
        'name_fr': 'Rabat'
      },
      {
        'id': '3',
        'region_id': '3',
        'name_ar': 'فاس',
        'name_en': 'Fez',
        'name_fr': 'Fès'
      },
      {
        'id': '4',
        'region_id': '4',
        'name_ar': 'مراكش',
        'name_en': 'Marrakech',
        'name_fr': 'Marrakech'
      },
      {
        'id': '5',
        'region_id': '5',
        'name_ar': 'أكادير',
        'name_en': 'Agadir',
        'name_fr': 'Agadir'
      },
    ];

    for (final city in sampleCities) {
      await db.insert('cities', {
        ...city,
        'created_at': DateTime.now().toIso8601String(),
      });
    }

    debugPrint('✅ تم إدراج ${sampleCities.length} مدينة أساسية');
  }

  /// إدراج الحرف
  Future<void> _insertCrafts(Database db) async {
    final crafts = [
      {
        'id': 1,
        'name_ar': 'كهربائي',
        'name_fr': 'Électricien',
        'name_en': 'Electrician'
      },
      {'id': 2, 'name_ar': 'سباك', 'name_fr': 'Plombier', 'name_en': 'Plumber'},
      {
        'id': 3,
        'name_ar': 'نجار',
        'name_fr': 'Menuisier',
        'name_en': 'Carpenter'
      },
      {'id': 4, 'name_ar': 'بناء', 'name_fr': 'Maçon', 'name_en': 'Mason'},
      {'id': 5, 'name_ar': 'دهان', 'name_fr': 'Peintre', 'name_en': 'Painter'},
      {
        'id': 6,
        'name_ar': 'حداد',
        'name_fr': 'Forgeron',
        'name_en': 'Blacksmith'
      },
      {
        'id': 7,
        'name_ar': 'ميكانيكي',
        'name_fr': 'Mécanicien',
        'name_en': 'Mechanic'
      },
      {'id': 8, 'name_ar': 'خياط', 'name_fr': 'Tailleur', 'name_en': 'Tailor'},
      {'id': 9, 'name_ar': 'حلاق', 'name_fr': 'Coiffeur', 'name_en': 'Barber'},
      {'id': 10, 'name_ar': 'طباخ', 'name_fr': 'Cuisinier', 'name_en': 'Cook'},
    ];

    for (final craft in crafts) {
      await db.insert('crafts', {
        ...craft,
        'created_at': DateTime.now().toIso8601String(),
      });
    }
  }

  /// إغلاق قاعدة البيانات
  Future<void> close() async {
    final db = _database;
    if (db != null) {
      await db.close();
      _database = null;
      debugPrint('🔒 تم إغلاق قاعدة البيانات');
    }
  }

  /// حذف قاعدة البيانات
  Future<void> deleteDatabase() async {
    try {
      await close();
      final databasesPath = await getDatabasesPath();
      final path = join(databasesPath, _databaseName);
      await databaseFactory.deleteDatabase(path);
      debugPrint('🗑️ تم حذف قاعدة البيانات: $path');
    } catch (e) {
      debugPrint('❌ خطأ في حذف قاعدة البيانات: $e');
      rethrow;
    }
  }
}
