import 'package:flutter/material.dart';
import '../constants/app_colors.dart';

class CraftSelector extends StatefulWidget {
  final String? selectedCraftId;
  final Function(String? craftId) onSelectionChanged;
  final String craftHint;

  const CraftSelector({
    super.key,
    this.selectedCraftId,
    required this.onSelectionChanged,
    this.craftHint = 'اختر الحرفة',
  });

  @override
  State<CraftSelector> createState() => _CraftSelectorState();
}

class _CraftSelectorState extends State<CraftSelector> {
  List<Map<String, dynamic>> _crafts = [];
  String? _selectedCraftId;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _selectedCraftId = widget.selectedCraftId;
    _loadData();
  }

  Future<void> _loadData() async {
    try {
      // استخدام البيانات المدمجة مباشرة
      _crafts = _getHardcodedCrafts();

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      debugPrint('خطأ في تحميل بيانات الحرف: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Column(
        children: [
          SizedBox(height: 16),
          Center(child: CircularProgressIndicator()),
          SizedBox(height: 16),
          Text('جاري تحميل الحرف...'),
        ],
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // اختيار الحرفة
        const Text(
          'الحرفة *',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            border: Border.all(color: Colors.grey.shade300),
            borderRadius: BorderRadius.circular(8),
            color: Colors.white,
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: _selectedCraftId,
              hint: Text(
                widget.craftHint,
                style: TextStyle(color: Colors.grey.shade600),
              ),
              isExpanded: true,
              items: _crafts.map((craft) {
                return DropdownMenuItem<String>(
                  value: craft['id'].toString(),
                  child: Text(
                    craft['name_ar'] ?? '',
                    style: const TextStyle(fontSize: 16),
                  ),
                );
              }).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedCraftId = value;
                });
                widget.onSelectionChanged(_selectedCraftId);
              },
            ),
          ),
        ),

        // عرض عدد الحرف المتاحة
        const SizedBox(height: 8),
        Text(
          'متاح ${_crafts.length} حرفة ومهنة',
          style: const TextStyle(
            fontSize: 12,
            color: AppColors.primaryColor,
            fontStyle: FontStyle.italic,
          ),
        ),
      ],
    );
  }

  /// الحصول على الحرف المدمجة
  List<Map<String, dynamic>> _getHardcodedCrafts() {
    return [
      {
        'id': '1',
        'name_ar': 'أجزاء الدراجات',
        'name_fr': 'Pièces de vélos',
        'name_en': 'Bicycle Parts'
      },
      {
        'id': '2',
        'name_ar': 'أجزاء السيارات',
        'name_fr': 'Pièces automobiles',
        'name_en': 'Car Parts'
      },
      {
        'id': '3',
        'name_ar': 'أحذية الرجال',
        'name_fr': 'Chaussures hommes',
        'name_en': 'Men\'s Shoes'
      },
      {
        'id': '4',
        'name_ar': 'أحذية النساء',
        'name_fr': 'Chaussures femmes',
        'name_en': 'Women\'s Shoes'
      },
      {
        'id': '5',
        'name_ar': 'أعمال البستنة',
        'name_fr': 'Jardinage',
        'name_en': 'Gardening'
      },
      {
        'id': '6',
        'name_ar': 'أعمال البناء',
        'name_fr': 'Construction',
        'name_en': 'Construction'
      },
      {
        'id': '7',
        'name_ar': 'أعمال الترجمة',
        'name_fr': 'Traduction',
        'name_en': 'Translation'
      },
      {
        'id': '8',
        'name_ar': 'أعمال التصوير',
        'name_fr': 'Photographie',
        'name_en': 'Photography'
      },
      {
        'id': '9',
        'name_ar': 'أعمال الجبس',
        'name_fr': 'Plâtrerie',
        'name_en': 'Plastering'
      },
      {
        'id': '10',
        'name_ar': 'أعمال الحدادة',
        'name_fr': 'Ferronnerie',
        'name_en': 'Blacksmithing'
      },
      {
        'id': '11',
        'name_ar': 'أعمال الخزف',
        'name_fr': 'Céramique',
        'name_en': 'Ceramics'
      },
      {
        'id': '12',
        'name_ar': 'أعمال الخياطة',
        'name_fr': 'Couture',
        'name_en': 'Sewing'
      },
      {
        'id': '13',
        'name_ar': 'أعمال الرخام',
        'name_fr': 'Marbrerie',
        'name_en': 'Marble Work'
      },
      {
        'id': '14',
        'name_ar': 'أعمال الرسم',
        'name_fr': 'Peinture',
        'name_en': 'Painting'
      },
      {
        'id': '15',
        'name_ar': 'أعمال الزراعة',
        'name_fr': 'Agriculture',
        'name_en': 'Agriculture'
      },
      {
        'id': '16',
        'name_ar': 'أعمال الزليج',
        'name_fr': 'Zellige',
        'name_en': 'Zellige'
      },
      {
        'id': '17',
        'name_ar': 'أعمال السباكة',
        'name_fr': 'Plomberie',
        'name_en': 'Plumbing'
      },
      {
        'id': '18',
        'name_ar': 'أعمال الصباغة',
        'name_fr': 'Teinture',
        'name_en': 'Dyeing'
      },
      {
        'id': '19',
        'name_ar': 'أعمال الطبخ',
        'name_fr': 'Cuisine',
        'name_en': 'Cooking'
      },
      {
        'id': '20',
        'name_ar': 'أعمال المحاسبة',
        'name_fr': 'Comptabilité',
        'name_en': 'Accounting'
      },
      {
        'id': '21',
        'name_ar': 'أعمال الميكانيك',
        'name_fr': 'Mécanique',
        'name_en': 'Mechanics'
      },
      {
        'id': '22',
        'name_ar': 'أعمال النظافة',
        'name_fr': 'Nettoyage',
        'name_en': 'Cleaning'
      },
      {
        'id': '23',
        'name_ar': 'إدارة الحسابات',
        'name_fr': 'Gestion comptable',
        'name_en': 'Account Management'
      },
      {
        'id': '24',
        'name_ar': 'إدارة المشاريع',
        'name_fr': 'Gestion de projets',
        'name_en': 'Project Management'
      },
      {
        'id': '25',
        'name_ar': 'إصلاح الأحذية',
        'name_fr': 'Réparation chaussures',
        'name_en': 'Shoe Repair'
      },
      {
        'id': '26',
        'name_ar': 'إصلاح الأواني',
        'name_fr': 'Réparation ustensiles',
        'name_en': 'Utensil Repair'
      },
      {
        'id': '27',
        'name_ar': 'إصلاح التلفاز',
        'name_fr': 'Réparation TV',
        'name_en': 'TV Repair'
      },
      {
        'id': '28',
        'name_ar': 'إصلاح الحاسوب',
        'name_fr': 'Réparation ordinateur',
        'name_en': 'Computer Repair'
      },
      {
        'id': '29',
        'name_ar': 'إصلاح الدراجات',
        'name_fr': 'Réparation vélos',
        'name_en': 'Bicycle Repair'
      },
      {
        'id': '30',
        'name_ar': 'إصلاح الساعات',
        'name_fr': 'Réparation montres',
        'name_en': 'Watch Repair'
      },
      {
        'id': '31',
        'name_ar': 'إصلاح الطابعات',
        'name_fr': 'Réparation imprimantes',
        'name_en': 'Printer Repair'
      },
      {
        'id': '32',
        'name_ar': 'إصلاح العجلات',
        'name_fr': 'Réparation roues',
        'name_en': 'Wheel Repair'
      },
      {
        'id': '33',
        'name_ar': 'إصلاح المفاتيح',
        'name_fr': 'Réparation clés',
        'name_en': 'Key Repair'
      },
      {
        'id': '34',
        'name_ar': 'إصلاح الهواتف',
        'name_fr': 'Réparation téléphones',
        'name_en': 'Phone Repair'
      },
      {
        'id': '35',
        'name_ar': 'الأعشاب والزيوت',
        'name_fr': 'Herbes et huiles',
        'name_en': 'Herbs and Oils'
      },
      {
        'id': '36',
        'name_ar': 'الأعمال المكتبية',
        'name_fr': 'Travaux de bureau',
        'name_en': 'Office Work'
      },
      {
        'id': '37',
        'name_ar': 'الألومنيوم والزجاج',
        'name_fr': 'Aluminium et verre',
        'name_en': 'Aluminum and Glass'
      },
      {
        'id': '38',
        'name_ar': 'الأواني الفخارية',
        'name_fr': 'Poterie',
        'name_en': 'Pottery'
      },
      {
        'id': '39',
        'name_ar': 'الإرساليات',
        'name_fr': 'Envois',
        'name_en': 'Shipments'
      },
      {
        'id': '40',
        'name_ar': 'الإرشاد السياحي',
        'name_fr': 'Guide touristique',
        'name_en': 'Tour Guide'
      },
      {
        'id': '41',
        'name_ar': 'الاستشارة القانونية',
        'name_fr': 'Conseil juridique',
        'name_en': 'Legal Consultation'
      },
      {
        'id': '42',
        'name_ar': 'الإشهار والتسويق',
        'name_fr': 'Publicité et marketing',
        'name_en': 'Advertising and Marketing'
      },
      {
        'id': '43',
        'name_ar': 'البيض والدجاج',
        'name_fr': 'Œufs et volaille',
        'name_en': 'Eggs and Poultry'
      },
      {
        'id': '44',
        'name_ar': 'التدريب الرياضي',
        'name_fr': 'Entraînement sportif',
        'name_en': 'Sports Training'
      },
      {
        'id': '45',
        'name_ar': 'الثلاجات والسخان',
        'name_fr': 'Réfrigérateurs et chauffe-eau',
        'name_en': 'Refrigerators and Water Heaters'
      },
      {
        'id': '46',
        'name_ar': 'الحلاقة والتجميل',
        'name_fr': 'Coiffure et beauté',
        'name_en': 'Hairdressing and Beauty'
      },
      {
        'id': '47',
        'name_ar': 'الحياكة التقليدية',
        'name_fr': 'Tissage traditionnel',
        'name_en': 'Traditional Weaving'
      },
      {
        'id': '48',
        'name_ar': 'الخضر والفواكه',
        'name_fr': 'Légumes et fruits',
        'name_en': 'Vegetables and Fruits'
      },
      {
        'id': '49',
        'name_ar': 'الرقية الشرعية',
        'name_fr': 'Roqya',
        'name_en': 'Islamic Healing'
      },
      {
        'id': '50',
        'name_ar': 'الفن والتنشيط',
        'name_fr': 'Art et animation',
        'name_en': 'Art and Animation'
      },
      {
        'id': '51',
        'name_ar': 'الكتابة العمومية والعدول',
        'name_fr': 'Écrivain public et notaire',
        'name_en': 'Public Writing and Notary'
      },
      {
        'id': '52',
        'name_ar': 'الماء والكهرباء',
        'name_fr': 'Eau et électricité',
        'name_en': 'Water and Electricity'
      },
      {
        'id': '53',
        'name_ar': 'المجزرة',
        'name_fr': 'Boucherie',
        'name_en': 'Butchery'
      },
      {
        'id': '54',
        'name_ar': 'المجوهرات',
        'name_fr': 'Bijouterie',
        'name_en': 'Jewelry'
      },
      {
        'id': '55',
        'name_ar': 'المساعدة الطبية',
        'name_fr': 'Assistance médicale',
        'name_en': 'Medical Assistance'
      },
      {
        'id': '56',
        'name_ar': 'الملابس النسائية',
        'name_fr': 'Vêtements femmes',
        'name_en': 'Women\'s Clothing'
      },
      {
        'id': '57',
        'name_ar': 'المواد الغذائية',
        'name_fr': 'Produits alimentaires',
        'name_en': 'Food Products'
      },
      {
        'id': '58',
        'name_ar': 'تدريب الحيوانات',
        'name_fr': 'Dressage d\'animaux',
        'name_en': 'Animal Training'
      },
      {
        'id': '59',
        'name_ar': 'تربية الطيور',
        'name_fr': 'Élevage d\'oiseaux',
        'name_en': 'Bird Breeding'
      },
      {
        'id': '60',
        'name_ar': 'تزيين العرائس',
        'name_fr': 'Maquillage mariée',
        'name_en': 'Bridal Makeup'
      },
      {
        'id': '61',
        'name_ar': 'تزيين الواجهات',
        'name_fr': 'Décoration façades',
        'name_en': 'Facade Decoration'
      },
      {
        'id': '62',
        'name_ar': 'تصميم التطبيقات',
        'name_fr': 'Conception d\'applications',
        'name_en': 'App Design'
      },
      {
        'id': '63',
        'name_ar': 'تصميم المواقع',
        'name_fr': 'Conception web',
        'name_en': 'Web Design'
      },
      {
        'id': '64',
        'name_ar': 'تعليم السياقة',
        'name_fr': 'Auto-école',
        'name_en': 'Driving School'
      },
      {
        'id': '65',
        'name_ar': 'تعليم اللغات',
        'name_fr': 'Enseignement langues',
        'name_en': 'Language Teaching'
      },
      {
        'id': '66',
        'name_ar': 'تغليف الأثاث',
        'name_fr': 'Emballage mobilier',
        'name_en': 'Furniture Packaging'
      },
      {
        'id': '67',
        'name_ar': 'تموين الحفلات',
        'name_fr': 'Traiteur événements',
        'name_en': 'Event Catering'
      },
      {
        'id': '68',
        'name_ar': 'دار الضيافة',
        'name_fr': 'Maison d\'hôtes',
        'name_en': 'Guest House'
      },
      {
        'id': '69',
        'name_ar': 'صناعة الحلويات',
        'name_fr': 'Pâtisserie',
        'name_en': 'Pastry Making'
      },
      {
        'id': '70',
        'name_ar': 'طب الأسنان',
        'name_fr': 'Dentisterie',
        'name_en': 'Dentistry'
      },
      {
        'id': '71',
        'name_ar': 'فن النقش والنحت',
        'name_fr': 'Gravure et sculpture',
        'name_en': 'Engraving and Sculpture'
      },
      {
        'id': '72',
        'name_ar': 'قيادة الشاحنات',
        'name_fr': 'Conduite poids lourds',
        'name_en': 'Truck Driving'
      },
      {
        'id': '73',
        'name_ar': 'كهرباء السيارات',
        'name_fr': 'Électricité automobile',
        'name_en': 'Automotive Electrical'
      },
      {
        'id': '74',
        'name_ar': 'محلات الأثاث',
        'name_fr': 'Magasins de meubles',
        'name_en': 'Furniture Stores'
      },
      {
        'id': '75',
        'name_ar': 'محلات الأسماك',
        'name_fr': 'Poissonnerie',
        'name_en': 'Fish Market'
      },
      {
        'id': '76',
        'name_ar': 'محلات الأكل',
        'name_fr': 'Restaurants',
        'name_en': 'Food Stores'
      },
      {
        'id': '77',
        'name_ar': 'محلات السيارات',
        'name_fr': 'Concessionnaires auto',
        'name_en': 'Car Dealerships'
      },
      {
        'id': '78',
        'name_ar': 'محلات العقاقير',
        'name_fr': 'Pharmacies',
        'name_en': 'Pharmacies'
      },
      {
        'id': '79',
        'name_ar': 'محلات الهواتف',
        'name_fr': 'Magasins téléphones',
        'name_en': 'Phone Stores'
      },
      {
        'id': '80',
        'name_ar': 'مراقبة السيارات',
        'name_fr': 'Contrôle technique auto',
        'name_en': 'Vehicle Inspection'
      },
      {
        'id': '81',
        'name_ar': 'مراكز السباحة',
        'name_fr': 'Centres de natation',
        'name_en': 'Swimming Centers'
      },
      {
        'id': '82',
        'name_ar': 'معاصر الزيتون',
        'name_fr': 'Huileries d\'olive',
        'name_en': 'Olive Oil Mills'
      },
      {
        'id': '83',
        'name_ar': 'ملابس الأطفال',
        'name_fr': 'Vêtements enfants',
        'name_en': 'Children\'s Clothing'
      },
      {
        'id': '84',
        'name_ar': 'ملابس الرجال',
        'name_fr': 'Vêtements hommes',
        'name_en': 'Men\'s Clothing'
      },
      {
        'id': '85',
        'name_ar': 'نجارة الخشب',
        'name_fr': 'Menuiserie bois',
        'name_en': 'Wood Carpentry'
      },
    ];
  }
}
