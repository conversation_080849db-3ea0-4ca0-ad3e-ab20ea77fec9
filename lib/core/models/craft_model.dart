/// نموذج الحرفة
class CraftModel {
  final int id;
  final String nameAr;
  final String nameFr;
  final String nameEn;
  final String? description;
  final bool isActive;
  final DateTime createdAt;

  CraftModel({
    required this.id,
    required this.nameAr,
    required this.nameFr,
    required this.nameEn,
    this.description,
    this.isActive = true,
    required this.createdAt,
  });

  /// إنشاء نموذج من Map
  factory CraftModel.fromMap(Map<String, dynamic> map) {
    return CraftModel(
      id: map['id']?.toInt() ?? 0,
      nameAr: map['name_ar'] ?? '',
      nameFr: map['name_fr'] ?? '',
      nameEn: map['name_en'] ?? '',
      description: map['description'],
      isActive: (map['is_active'] ?? 1) == 1,
      createdAt: DateTime.parse(map['created_at'] ?? DateTime.now().toIso8601String()),
    );
  }

  /// تحويل النموذج إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name_ar': nameAr,
      'name_fr': nameFr,
      'name_en': nameEn,
      'description': description,
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
    };
  }

  /// إنشاء نسخة محدثة من النموذج
  CraftModel copyWith({
    int? id,
    String? nameAr,
    String? nameFr,
    String? nameEn,
    String? description,
    bool? isActive,
    DateTime? createdAt,
  }) {
    return CraftModel(
      id: id ?? this.id,
      nameAr: nameAr ?? this.nameAr,
      nameFr: nameFr ?? this.nameFr,
      nameEn: nameEn ?? this.nameEn,
      description: description ?? this.description,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  String toString() {
    return 'CraftModel(id: $id, nameAr: $nameAr, nameFr: $nameFr, nameEn: $nameEn)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CraftModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  /// الحصول على الاسم حسب اللغة
  String getName(String language) {
    switch (language.toLowerCase()) {
      case 'ar':
        return nameAr;
      case 'fr':
        return nameFr;
      case 'en':
        return nameEn;
      default:
        return nameAr; // الافتراضي العربية
    }
  }

  /// التحقق من صحة البيانات
  bool get isValid {
    return nameAr.isNotEmpty && nameFr.isNotEmpty && nameEn.isNotEmpty;
  }

  /// البحث في النص
  bool containsQuery(String query, {String language = 'ar'}) {
    if (query.isEmpty) return true;
    
    final searchQuery = query.toLowerCase();
    final name = getName(language).toLowerCase();
    final desc = (description ?? '').toLowerCase();
    
    return name.contains(searchQuery) || 
           desc.contains(searchQuery) ||
           nameAr.toLowerCase().contains(searchQuery) ||
           nameFr.toLowerCase().contains(searchQuery) ||
           nameEn.toLowerCase().contains(searchQuery);
  }
}
