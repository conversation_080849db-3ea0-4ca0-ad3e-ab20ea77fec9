import 'package:flutter/material.dart';
import '../../shared/constants/app_colors.dart';
import '../../shared/widgets/custom_button.dart';
import '../../core/models/order_model.dart';
import '../../core/repositories/order_repository.dart';
import 'rate_order_screen.dart';

class OrderDetailsScreen extends StatefulWidget {
  final OrderModel order;
  final bool isCustomerView;

  const OrderDetailsScreen({
    super.key,
    required this.order,
    this.isCustomerView = true,
  });

  @override
  State<OrderDetailsScreen> createState() => _OrderDetailsScreenState();
}

class _OrderDetailsScreenState extends State<OrderDetailsScreen> {
  final _orderRepository = OrderRepository();
  late OrderModel _currentOrder;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _currentOrder = widget.order;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تفاصيل الطلب'),
        backgroundColor: AppColors.primaryColor,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // حالة الطلب
            _buildStatusCard(),
            
            const SizedBox(height: 16),
            
            // معلومات أساسية
            _buildBasicInfo(),
            
            const SizedBox(height: 16),
            
            // تفاصيل الطلب
            _buildOrderDetails(),
            
            const SizedBox(height: 16),
            
            // معلومات التوقيت والسعر
            _buildScheduleAndPrice(),
            
            if (_currentOrder.customerNotes != null) ...[
              const SizedBox(height: 16),
              _buildNotesSection(),
            ],
            
            if (_currentOrder.craftsmanNotes != null) ...[
              const SizedBox(height: 16),
              _buildCraftsmanNotesSection(),
            ],
            
            if (_currentOrder.customerRating != null) ...[
              const SizedBox(height: 16),
              _buildRatingSection(),
            ],
            
            const SizedBox(height: 24),
            
            // أزرار الإجراءات
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Color(int.parse(_currentOrder.statusColor.replaceFirst('#', '0xFF'))),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Icon(
            _getStatusIcon(_currentOrder.status),
            color: Colors.white,
            size: 32,
          ),
          const SizedBox(height: 8),
          Text(
            _currentOrder.statusText,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'رقم الطلب: ${_currentOrder.id}',
            style: const TextStyle(
              color: Colors.white70,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBasicInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'معلومات أساسية',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            _buildInfoRow(
              icon: Icons.title,
              label: 'العنوان',
              value: _currentOrder.title,
            ),
            
            const SizedBox(height: 12),
            
            _buildInfoRow(
              icon: Icons.calendar_today,
              label: 'تاريخ الإنشاء',
              value: _formatDateTime(_currentOrder.createdAt),
            ),
            
            if (_currentOrder.completedAt != null) ...[
              const SizedBox(height: 12),
              _buildInfoRow(
                icon: Icons.check_circle,
                label: 'تاريخ الإكمال',
                value: _formatDateTime(_currentOrder.completedAt!),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildOrderDetails() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'تفاصيل الطلب',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[200]!),
              ),
              child: Text(
                _currentOrder.description,
                style: const TextStyle(fontSize: 16),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildScheduleAndPrice() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'التوقيت والسعر',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            if (_currentOrder.scheduledDate != null) ...[
              _buildInfoRow(
                icon: Icons.schedule,
                label: 'الموعد المحدد',
                value: _formatDateTime(_currentOrder.scheduledDate!),
              ),
              const SizedBox(height: 12),
            ],
            
            _buildInfoRow(
              icon: Icons.attach_money,
              label: 'السعر',
              value: _currentOrder.priceText,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotesSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'ملاحظات العميل',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue[200]!),
              ),
              child: Text(
                _currentOrder.customerNotes!,
                style: const TextStyle(fontSize: 16),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCraftsmanNotesSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'ملاحظات الحرفي',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.green[200]!),
              ),
              child: Text(
                _currentOrder.craftsmanNotes!,
                style: const TextStyle(fontSize: 16),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRatingSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'التقييم والمراجعة',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            Row(
              children: [
                const Icon(Icons.star, color: Colors.amber, size: 24),
                const SizedBox(width: 8),
                Text(
                  '${_currentOrder.customerRating!.toStringAsFixed(1)} من 5',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            
            if (_currentOrder.customerReview != null) ...[
              const SizedBox(height: 12),
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.amber[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.amber[200]!),
                ),
                child: Text(
                  _currentOrder.customerReview!,
                  style: const TextStyle(fontSize: 16),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    List<Widget> buttons = [];

    if (widget.isCustomerView) {
      // أزرار العميل
      if (_currentOrder.canBeCancelled) {
        buttons.add(
          CustomButton(
            text: 'إلغاء الطلب',
            onPressed: _isLoading ? null : () => _updateOrderStatus(OrderStatus.cancelled),
            backgroundColor: Colors.red,
            isLoading: _isLoading,
          ),
        );
      }

      if (_currentOrder.canBeRated) {
        buttons.add(
          CustomButton(
            text: 'تقييم الطلب',
            onPressed: _navigateToRating,
          ),
        );
      }
    } else {
      // أزرار الحرفي
      if (_currentOrder.status == OrderStatus.pending) {
        buttons.addAll([
          CustomButton(
            text: 'قبول الطلب',
            onPressed: _isLoading ? null : () => _updateOrderStatus(OrderStatus.accepted),
            isLoading: _isLoading,
          ),
          const SizedBox(height: 12),
          CustomButton(
            text: 'رفض الطلب',
            onPressed: _isLoading ? null : () => _updateOrderStatus(OrderStatus.rejected),
            backgroundColor: Colors.red,
            isLoading: _isLoading,
          ),
        ]);
      } else if (_currentOrder.status == OrderStatus.accepted) {
        buttons.add(
          CustomButton(
            text: 'بدء العمل',
            onPressed: _isLoading ? null : () => _updateOrderStatus(OrderStatus.inProgress),
            isLoading: _isLoading,
          ),
        );
      } else if (_currentOrder.status == OrderStatus.inProgress) {
        buttons.add(
          CustomButton(
            text: 'إكمال العمل',
            onPressed: _isLoading ? null : () => _updateOrderStatus(OrderStatus.completed),
            backgroundColor: Colors.green,
            isLoading: _isLoading,
          ),
        );
      }
    }

    return Column(
      children: buttons,
    );
  }

  Widget _buildInfoRow({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(icon, color: AppColors.primaryColor, size: 20),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(height: 2),
              Text(
                value,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  IconData _getStatusIcon(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return Icons.hourglass_empty;
      case OrderStatus.accepted:
        return Icons.check_circle_outline;
      case OrderStatus.inProgress:
        return Icons.work_outline;
      case OrderStatus.completed:
        return Icons.check_circle;
      case OrderStatus.cancelled:
        return Icons.cancel_outlined;
      case OrderStatus.rejected:
        return Icons.close;
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} - ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  Future<void> _updateOrderStatus(OrderStatus newStatus) async {
    setState(() {
      _isLoading = true;
    });

    try {
      final success = await _orderRepository.updateOrderStatus(_currentOrder.id!, newStatus);
      
      if (success) {
        setState(() {
          _currentOrder = _currentOrder.copyWith(
            status: newStatus,
            updatedAt: DateTime.now(),
            completedAt: newStatus == OrderStatus.completed ? DateTime.now() : null,
          );
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('تم تحديث حالة الطلب إلى: ${_currentOrder.statusText}'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        throw Exception('فشل في تحديث حالة الطلب');
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحديث الطلب: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _navigateToRating() async {
    final result = await Navigator.of(context).push<bool>(
      MaterialPageRoute(
        builder: (context) => RateOrderScreen(order: _currentOrder),
      ),
    );

    if (result == true) {
      // إعادة تحميل تفاصيل الطلب
      final updatedOrder = await _orderRepository.getOrderById(_currentOrder.id!);
      if (updatedOrder != null) {
        setState(() {
          _currentOrder = updatedOrder;
        });
      }
    }
  }
}
