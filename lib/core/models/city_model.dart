ù)ù)==/// نموذج المدينة المغربية
class CityModel {
  final String id;
  final String regionId;
  final String nameAr;
  final String nameEn;
  final String nameFr;
  final DateTime createdAt;

  const CityModel({
    required this.id,
    required this.regionId,
    required this.nameAr,
    required this.nameEn,
    required this.nameFr,
    required this.createdAt,
  });

  factory CityModel.fromMap(Map<String, dynamic> map) {
    return CityModel(
      id: map['id']?.toString() ?? '',
      regionId: map['region_id']?.toString() ?? '',
      nameAr: map['name_ar']?.toString() ?? '',
      nameEn: map['name_en']?.toString() ?? '',
      nameFr: map['name_fr']?.toString() ?? '',
      createdAt: DateTime.tryParse(map['created_at']?.toString() ?? '') ??
          DateTime.now(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'region_id': regionId,
      'name_ar': nameAr,
      'name_en': nameEn,
      'name_fr': nameFr,
      'created_at': createdAt.toIso8601String(),
    };
  }

  CityModel copyWith({
    String? id,
    String? regionId,
    String? nameAr,
    String? nameEn,
    String? nameFr,
    DateTime? createdAt,
  }) {
    return CityModel(
      id: id ?? this.id,
      regionId: regionId ?? this.regionId,
      nameAr: nameAr ?? this.nameAr,
      nameEn: nameEn ?? this.nameEn,
      nameFr: nameFr ?? this.nameFr,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  /// الحصول على الاسم حسب اللغة
  String getName([String language = 'ar']) {
    switch (language) {
      case 'fr':
        return nameFr;
      case 'en':
        return nameEn;
      case 'ar':
      default:
        return nameAr;
    }
  }

  @override
  String toString() {
    return 'CityModel(id: $id, regionId: $regionId, nameAr: $nameAr, nameEn: $nameEn, nameFr: $nameFr)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CityModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// إحصائيات قاعدة البيانات
class DatabaseStats {
  final int regionsCount;
  final int citiesCount;
  final int craftsCount;
  final int categoriesCount;
  final int craftsmenCount;
  final int customersCount;

  DatabaseStats({
    required this.regionsCount,
    required this.citiesCount,
    required this.craftsCount,
    required this.categoriesCount,
    required this.craftsmenCount,
    required this.customersCount,
  });

  factory DatabaseStats.fromMap(Map<String, dynamic> map) {
    return DatabaseStats(
      regionsCount: map['regions_count']?.toInt() ?? 0,
      citiesCount: map['cities_count']?.toInt() ?? 0,
      craftsCount: map['crafts_count']?.toInt() ?? 0,
      categoriesCount: map['categories_count']?.toInt() ?? 0,
      craftsmenCount: map['craftsmen_count']?.toInt() ?? 0,
      customersCount: map['customers_count']?.toInt() ?? 0,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'regions_count': regionsCount,
      'cities_count': citiesCount,
      'crafts_count': craftsCount,
      'categories_count': categoriesCount,
      'craftsmen_count': craftsmenCount,
      'customers_count': customersCount,
    };
  }

  @override
  String toString() {
    return 'DatabaseStats(regions: $regionsCount, cities: $citiesCount, crafts: $craftsCount, categories: $categoriesCount, craftsmen: $craftsmenCount, customers: $customersCount)';
  }
}

/// نتائج البحث المتقدم
class SearchResult<T> {
  final List<T> results;
  final int totalCount;
  final String query;
  final Map<String, dynamic> filters;

  SearchResult({
    required this.results,
    required this.totalCount,
    required this.query,
    this.filters = const {},
  });

  bool get hasResults => results.isNotEmpty;
  bool get isEmpty => results.isEmpty;

  @override
  String toString() {
    return 'SearchResult(query: $query, totalCount: $totalCount, resultsCount: ${results.length})';
  }
}
