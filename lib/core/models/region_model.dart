import 'city_model.dart';

/// نموذج الجهة المغربية
class RegionModel {
  final String id;
  final String nameAr;
  final String nameFr;
  final String nameEn;
  final DateTime createdAt;
  final List<CityModel>? cities;

  const RegionModel({
    required this.id,
    required this.nameAr,
    required this.nameFr,
    required this.nameEn,
    required this.createdAt,
    this.cities,
  });

  /// إنشاء نموذج من خريطة
  factory RegionModel.fromMap(Map<String, dynamic> map) {
    return RegionModel(
      id: map['id']?.toString() ?? '',
      nameAr: map['name_ar']?.toString() ?? '',
      nameFr: map['name_fr']?.toString() ?? '',
      nameEn: map['name_en']?.toString() ?? '',
      createdAt: DateTime.tryParse(map['created_at']?.toString() ?? '') ?? DateTime.now(),
      cities: null, // سيتم تحميلها منفصلة
    );
  }

  /// تحويل إلى خريطة
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name_ar': nameAr,
      'name_fr': nameFr,
      'name_en': nameEn,
      'created_at': createdAt.toIso8601String(),
    };
  }

  /// نسخ مع تعديل
  RegionModel copyWith({
    String? id,
    String? nameAr,
    String? nameFr,
    String? nameEn,
    DateTime? createdAt,
    List<CityModel>? cities,
  }) {
    return RegionModel(
      id: id ?? this.id,
      nameAr: nameAr ?? this.nameAr,
      nameFr: nameFr ?? this.nameFr,
      nameEn: nameEn ?? this.nameEn,
      createdAt: createdAt ?? this.createdAt,
      cities: cities ?? this.cities,
    );
  }

  /// الحصول على الاسم حسب اللغة
  String getName([String language = 'ar']) {
    switch (language) {
      case 'fr':
        return nameFr;
      case 'en':
        return nameEn;
      case 'ar':
      default:
        return nameAr;
    }
  }

  /// عدد المدن في الجهة
  int get citiesCount => cities?.length ?? 0;

  /// هل الجهة تحتوي على مدن
  bool get hasCities => citiesCount > 0;

  @override
  String toString() {
    return 'RegionModel(id: $id, nameAr: $nameAr, citiesCount: $citiesCount)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is RegionModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
