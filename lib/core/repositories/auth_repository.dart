import '../database/database_helper.dart';

/// مستودع المصادقة
class AuthRepository {
  final DatabaseHelper _dbHelper = DatabaseHelper();

  /// حفظ كلمة المرور المشفرة
  Future<bool> savePasswordHash({
    required int userId,
    required String userType,
    required String passwordHash,
  }) async {
    try {
      final db = await _dbHelper.database;
      
      final result = await db.insert('user_auth', {
        'user_id': userId,
        'user_type': userType,
        'password_hash': passwordHash,
        'created_at': DateTime.now().toIso8601String(),
      });
      
      return result > 0;
    } catch (e) {
      print('خطأ في حفظ كلمة المرور: $e');
      return false;
    }
  }

  /// الحصول على كلمة المرور المشفرة
  Future<String?> getPasswordHash({
    required int userId,
    required String userType,
  }) async {
    try {
      final db = await _dbHelper.database;
      
      final List<Map<String, dynamic>> maps = await db.query(
        'user_auth',
        columns: ['password_hash'],
        where: 'user_id = ? AND user_type = ?',
        whereArgs: [userId, userType],
        limit: 1,
      );

      if (maps.isEmpty) return null;
      return maps.first['password_hash'] as String;
    } catch (e) {
      print('خطأ في الحصول على كلمة المرور: $e');
      return null;
    }
  }

  /// تحديث كلمة المرور
  Future<bool> updatePasswordHash({
    required int userId,
    required String userType,
    required String newPasswordHash,
  }) async {
    try {
      final db = await _dbHelper.database;
      
      final result = await db.update(
        'user_auth',
        {
          'password_hash': newPasswordHash,
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'user_id = ? AND user_type = ?',
        whereArgs: [userId, userType],
      );
      
      return result > 0;
    } catch (e) {
      print('خطأ في تحديث كلمة المرور: $e');
      return false;
    }
  }

  /// حذف بيانات المصادقة
  Future<bool> deleteAuth({
    required int userId,
    required String userType,
  }) async {
    try {
      final db = await _dbHelper.database;
      
      final result = await db.delete(
        'user_auth',
        where: 'user_id = ? AND user_type = ?',
        whereArgs: [userId, userType],
      );
      
      return result > 0;
    } catch (e) {
      print('خطأ في حذف بيانات المصادقة: $e');
      return false;
    }
  }

  /// التحقق من وجود مصادقة للمستخدم
  Future<bool> hasAuth({
    required int userId,
    required String userType,
  }) async {
    try {
      final db = await _dbHelper.database;
      
      final List<Map<String, dynamic>> maps = await db.query(
        'user_auth',
        columns: ['id'],
        where: 'user_id = ? AND user_type = ?',
        whereArgs: [userId, userType],
        limit: 1,
      );

      return maps.isNotEmpty;
    } catch (e) {
      print('خطأ في التحقق من المصادقة: $e');
      return false;
    }
  }

  /// الحصول على إحصائيات المصادقة
  Future<Map<String, dynamic>> getAuthStatistics() async {
    try {
      final db = await _dbHelper.database;
      
      // إجمالي المستخدمين المسجلين
      final totalResult = await db.rawQuery('SELECT COUNT(*) as count FROM user_auth');
      final totalUsers = totalResult.first['count'] as int;
      
      // المستخدمين حسب النوع
      final customerResult = await db.rawQuery('SELECT COUNT(*) as count FROM user_auth WHERE user_type = "customer"');
      final totalCustomers = customerResult.first['count'] as int;
      
      final craftsmanResult = await db.rawQuery('SELECT COUNT(*) as count FROM user_auth WHERE user_type = "craftsman"');
      final totalCraftsmen = craftsmanResult.first['count'] as int;
      
      // المستخدمين الجدد هذا الشهر
      final now = DateTime.now();
      final startOfMonth = DateTime(now.year, now.month, 1);
      final newThisMonthResult = await db.rawQuery(
        'SELECT COUNT(*) as count FROM user_auth WHERE created_at >= ?',
        [startOfMonth.toIso8601String()],
      );
      final newThisMonth = newThisMonthResult.first['count'] as int;
      
      return {
        'total_users': totalUsers,
        'total_customers': totalCustomers,
        'total_craftsmen': totalCraftsmen,
        'new_this_month': newThisMonth,
      };
    } catch (e) {
      print('خطأ في الحصول على إحصائيات المصادقة: $e');
      return {};
    }
  }

  /// تنظيف بيانات المصادقة القديمة (للمستخدمين المحذوفين)
  Future<void> cleanupOrphanedAuth() async {
    try {
      final db = await _dbHelper.database;
      
      // حذف مصادقة العملاء المحذوفين
      await db.rawDelete('''
        DELETE FROM user_auth 
        WHERE user_type = "customer" 
        AND user_id NOT IN (SELECT id FROM customers)
      ''');
      
      // حذف مصادقة الحرفيين المحذوفين
      await db.rawDelete('''
        DELETE FROM user_auth 
        WHERE user_type = "craftsman" 
        AND user_id NOT IN (SELECT id FROM craftsmen)
      ''');
      
      print('تم تنظيف بيانات المصادقة القديمة');
    } catch (e) {
      print('خطأ في تنظيف بيانات المصادقة: $e');
    }
  }
}
