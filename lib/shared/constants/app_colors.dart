import 'package:flutter/material.dart';

/// ألوان التطبيق
class AppColors {
  // الألوان الأساسية
  static const Color primaryColor = Color(0xFF2E7D32);
  static const Color primaryLightColor = Color(0xFF60AD5E);
  static const Color primaryDarkColor = Color(0xFF005005);
  
  // الألوان الثانوية
  static const Color secondaryColor = Color(0xFF1976D2);
  static const Color secondaryLightColor = Color(0xFF63A4FF);
  static const Color secondaryDarkColor = Color(0xFF004BA0);
  
  // ألوان الحالة
  static const Color successColor = Color(0xFF4CAF50);
  static const Color warningColor = Color(0xFFFF9800);
  static const Color errorColor = Color(0xFFF44336);
  static const Color infoColor = Color(0xFF2196F3);
  
  // ألوان النص
  static const Color textPrimary = Color(0xFF212121);
  static const Color textSecondary = Color(0xFF757575);
  static const Color textHint = Color(0xFF9E9E9E);
  static const Color textOnPrimary = Colors.white;
  
  // ألوان الخلفية
  static const Color backgroundColor = Color(0xFFFAFAFA);
  static const Color surfaceColor = Colors.white;
  static const Color cardColor = Colors.white;
  
  // ألوان الحدود
  static const Color borderColor = Color(0xFFE0E0E0);
  static const Color dividerColor = Color(0xFFBDBDBD);
  
  // ألوان الظلال
  static const Color shadowColor = Color(0x1F000000);
  static const Color overlayColor = Color(0x80000000);
  
  // ألوان خاصة بالتطبيق
  static const Color craftmanColor = Color(0xFF795548);
  static const Color customerColor = Color(0xFF2196F3);
  static const Color ratingColor = Color(0xFFFFB300);
  
  // تدرجات لونية
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primaryColor, primaryLightColor],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient secondaryGradient = LinearGradient(
    colors: [secondaryColor, secondaryLightColor],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
}
