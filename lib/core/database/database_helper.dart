import 'dart:async';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'package:path/path.dart';
import 'package:flutter/foundation.dart';

/// مساعد قاعدة البيانات الرئيسي
class DatabaseHelper {
  static final DatabaseHelper _instance = DatabaseHelper._internal();
  factory DatabaseHelper() => _instance;
  DatabaseHelper._internal();

  static Database? _database;
  static const String _databaseName = 'elmokef.db';
  static const int _databaseVersion = 1;

  /// الحصول على قاعدة البيانات
  Future<Database> get database async {
    _database ??= await _initDatabase();
    return _database!;
  }

  /// تهيئة قاعدة البيانات
  Future<Database> _initDatabase() async {
    try {
      final databasesPath = await getDatabasesPath();
      final path = join(databasesPath, _databaseName);

      debugPrint('🗄️ تهيئة قاعدة البيانات في: $path');

      return await openDatabase(
        path,
        version: _databaseVersion,
        onCreate: _onCreate,
        onUpgrade: _onUpgrade,
        onOpen: _onOpen,
      );
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة قاعدة البيانات: $e');
      rethrow;
    }
  }

  /// إنشاء الجداول عند إنشاء قاعدة البيانات لأول مرة
  Future<void> _onCreate(Database db, int version) async {
    debugPrint('🆕 إنشاء جداول قاعدة البيانات...');

    // جدول الجهات
    await db.execute('''
      CREATE TABLE regions (
        id TEXT PRIMARY KEY,
        name_ar TEXT NOT NULL,
        name_fr TEXT NOT NULL,
        name_en TEXT NOT NULL,
        created_at TEXT NOT NULL
      )
    ''');

    // جدول المدن
    await db.execute('''
      CREATE TABLE cities (
        id TEXT PRIMARY KEY,
        region_id TEXT NOT NULL,
        name_ar TEXT NOT NULL,
        name_fr TEXT NOT NULL,
        name_en TEXT NOT NULL,
        created_at TEXT NOT NULL,
        FOREIGN KEY (region_id) REFERENCES regions (id)
      )
    ''');

    // جدول الحرف
    await db.execute('''
      CREATE TABLE crafts (
        id INTEGER PRIMARY KEY,
        name_ar TEXT NOT NULL,
        name_fr TEXT NOT NULL,
        name_en TEXT NOT NULL,
        description TEXT,
        is_active INTEGER DEFAULT 1,
        created_at TEXT NOT NULL
      )
    ''');

    // جدول العملاء
    await db.execute('''
      CREATE TABLE customers (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        email TEXT UNIQUE NOT NULL,
        phone TEXT NOT NULL,
        region_id TEXT NOT NULL,
        city_id TEXT NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        FOREIGN KEY (region_id) REFERENCES regions (id),
        FOREIGN KEY (city_id) REFERENCES cities (id)
      )
    ''');

    // جدول الحرفيين
    await db.execute('''
      CREATE TABLE craftsmen (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name TEXT NOT NULL,
        email TEXT UNIQUE NOT NULL,
        phone TEXT NOT NULL,
        craft_id TEXT NOT NULL,
        region_id TEXT NOT NULL,
        city_id TEXT NOT NULL,
        rating REAL DEFAULT 0.0,
        completed_orders INTEGER DEFAULT 0,
        is_active INTEGER DEFAULT 1,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        FOREIGN KEY (craft_id) REFERENCES crafts (id),
        FOREIGN KEY (region_id) REFERENCES regions (id),
        FOREIGN KEY (city_id) REFERENCES cities (id)
      )
    ''');

    // جدول الطلبات
    await db.execute('''
      CREATE TABLE orders (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        customer_id INTEGER NOT NULL,
        craftsman_id INTEGER NOT NULL,
        title TEXT NOT NULL,
        description TEXT NOT NULL,
        status TEXT DEFAULT 'pending',
        price REAL,
        scheduled_date TEXT,
        customer_notes TEXT,
        craftsman_notes TEXT,
        customer_rating REAL,
        customer_review TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        completed_at TEXT,
        FOREIGN KEY (customer_id) REFERENCES customers (id),
        FOREIGN KEY (craftsman_id) REFERENCES craftsmen (id)
      )
    ''');

    // جدول المصادقة
    await db.execute('''
      CREATE TABLE user_auth (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id INTEGER NOT NULL,
        user_type TEXT NOT NULL,
        password_hash TEXT NOT NULL,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        UNIQUE(user_id, user_type)
      )
    ''');

    // إنشاء الفهارس لتحسين الأداء
    await _createIndexes(db);

    // إدراج البيانات الأولية
    await _insertInitialData(db);

    debugPrint('✅ تم إنشاء جداول قاعدة البيانات بنجاح');
  }

  /// ترقية قاعدة البيانات
  Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    debugPrint(
        '🔄 ترقية قاعدة البيانات من الإصدار $oldVersion إلى $newVersion');
    // سيتم إضافة منطق الترقية هنا عند الحاجة
  }

  /// عند فتح قاعدة البيانات
  Future<void> _onOpen(Database db) async {
    debugPrint('📂 تم فتح قاعدة البيانات بنجاح');
    // تفعيل المفاتيح الخارجية
    await db.execute('PRAGMA foreign_keys = ON');
  }

  /// إنشاء الفهارس
  Future<void> _createIndexes(Database db) async {
    await db.execute('CREATE INDEX idx_customers_email ON customers (email)');
    await db.execute(
        'CREATE INDEX idx_customers_region_city ON customers (region_id, city_id)');
    await db.execute('CREATE INDEX idx_craftsmen_email ON craftsmen (email)');
    await db
        .execute('CREATE INDEX idx_craftsmen_craft ON craftsmen (craft_id)');
    await db.execute(
        'CREATE INDEX idx_craftsmen_region_city ON craftsmen (region_id, city_id)');
    await db.execute('CREATE INDEX idx_craftsmen_rating ON craftsmen (rating)');
    await db
        .execute('CREATE INDEX idx_orders_customer ON orders (customer_id)');
    await db
        .execute('CREATE INDEX idx_orders_craftsman ON orders (craftsman_id)');
    await db.execute('CREATE INDEX idx_orders_status ON orders (status)');
    await db
        .execute('CREATE INDEX idx_orders_created_at ON orders (created_at)');
    await db.execute(
        'CREATE INDEX idx_user_auth_user ON user_auth (user_id, user_type)');
  }

  /// إدراج البيانات الأولية
  Future<void> _insertInitialData(Database db) async {
    debugPrint('📝 إدراج البيانات الأولية...');

    // إدراج الجهات
    await _insertRegions(db);

    // إدراج المدن
    await _insertCities(db);

    // إدراج الحرف
    await _insertCrafts(db);

    debugPrint('✅ تم إدراج البيانات الأولية بنجاح');
  }

  /// إدراج الجهات
  Future<void> _insertRegions(Database db) async {
    final regions = [
      {
        'id': '1',
        'name_ar': 'الداخلة وادي الذهب',
        'name_fr': 'Dakhla-Oued Ed Dahab',
        'name_en': 'Dakhla-Oued Ed Dahab'
      },
      {
        'id': '2',
        'name_ar': 'الدار البيضاء سطات',
        'name_fr': 'Casablanca-Settat',
        'name_en': 'Casablanca-Settat'
      },
      {
        'id': '3',
        'name_ar': 'الرباط سلا القنيطرة',
        'name_fr': 'Rabat-Salé-Kénitra',
        'name_en': 'Rabat-Sale-Kenitra'
      },
      {
        'id': '4',
        'name_ar': 'الشرق',
        'name_fr': 'l\'Oriental',
        'name_en': 'Oriental'
      },
      {
        'id': '5',
        'name_ar': 'العيون الساقية الحمراء',
        'name_fr': 'Laâyoune-Sakia El Hamra',
        'name_en': 'Laayoune-Sakia El Hamra'
      },
      {
        'id': '6',
        'name_ar': 'بني ملال خنيفرة',
        'name_fr': 'Béni Mellal-Khénifra',
        'name_en': 'Beni Mellal-Khenifra'
      },
      {
        'id': '7',
        'name_ar': 'تانسيفت الحوز',
        'name_fr': 'Marrakech-Safi',
        'name_en': 'Marrakech-Safi'
      },
      {
        'id': '8',
        'name_ar': 'درعة تافيلالت',
        'name_fr': 'Drâa-Tafilalet',
        'name_en': 'Draa-Tafilalet'
      },
      {
        'id': '9',
        'name_ar': 'سوس ماسة',
        'name_fr': 'Souss-Massa',
        'name_en': 'Souss-Massa'
      },
      {
        'id': '10',
        'name_ar': 'كلميم واد نون',
        'name_fr': 'Guelmim-Oued Noun',
        'name_en': 'Guelmim-Oued Noun'
      },
      {
        'id': '11',
        'name_ar': 'فاس مكناس',
        'name_fr': 'Fès-Meknès',
        'name_en': 'Fes-Meknes'
      },
      {
        'id': '12',
        'name_ar': 'طنجة تطوان الحسيمة',
        'name_fr': 'Tanger-Tétouan-Al Hoceïma',
        'name_en': 'Tangier-Tetouan-Al Hoceima'
      },
    ];

    for (final region in regions) {
      await db.insert('regions', {
        ...region,
        'created_at': DateTime.now().toIso8601String(),
      });
    }
  }

  /// إدراج المدن (سيتم إضافة المزيد)
  Future<void> _insertCities(Database db) async {
    // سيتم إضافة جميع المدن الـ266 هنا
    // للاختصار، سأضيف بعض المدن كمثال
    final cities = [
      // جهة الداخلة وادي الذهب
      {
        'id': '1',
        'region_id': '1',
        'name_ar': 'الداخلة',
        'name_fr': 'Dakhla',
        'name_en': 'Dakhla'
      },
      {
        'id': '2',
        'region_id': '1',
        'name_ar': 'أوسرد',
        'name_fr': 'Aousserd',
        'name_en': 'Aousserd'
      },

      // جهة الدار البيضاء سطات
      {
        'id': '3',
        'region_id': '2',
        'name_ar': 'الدار البيضاء',
        'name_fr': 'Casablanca',
        'name_en': 'Casablanca'
      },
      {
        'id': '4',
        'region_id': '2',
        'name_ar': 'سطات',
        'name_fr': 'Settat',
        'name_en': 'Settat'
      },
      {
        'id': '5',
        'region_id': '2',
        'name_ar': 'برشيد',
        'name_fr': 'Berrechid',
        'name_en': 'Berrechid'
      },
      {
        'id': '6',
        'region_id': '2',
        'name_ar': 'المحمدية',
        'name_fr': 'Mohammedia',
        'name_en': 'Mohammedia'
      },

      // جهة الرباط سلا القنيطرة
      {
        'id': '7',
        'region_id': '3',
        'name_ar': 'الرباط',
        'name_fr': 'Rabat',
        'name_en': 'Rabat'
      },
      {
        'id': '8',
        'region_id': '3',
        'name_ar': 'سلا',
        'name_fr': 'Salé',
        'name_en': 'Sale'
      },
      {
        'id': '9',
        'region_id': '3',
        'name_ar': 'القنيطرة',
        'name_fr': 'Kénitra',
        'name_en': 'Kenitra'
      },
      {
        'id': '10',
        'region_id': '3',
        'name_ar': 'تمارة',
        'name_fr': 'Témara',
        'name_en': 'Temara'
      },

      // جهة الشرق
      {
        'id': '11',
        'region_id': '4',
        'name_ar': 'وجدة',
        'name_fr': 'Oujda',
        'name_en': 'Oujda'
      },
      {
        'id': '12',
        'region_id': '4',
        'name_ar': 'الناظور',
        'name_fr': 'Nador',
        'name_en': 'Nador'
      },
      {
        'id': '13',
        'region_id': '4',
        'name_ar': 'بركان',
        'name_fr': 'Berkane',
        'name_en': 'Berkane'
      },
      {
        'id': '14',
        'region_id': '4',
        'name_ar': 'تاوريرت',
        'name_fr': 'Taourirt',
        'name_en': 'Taourirt'
      },
      {
        'id': '15',
        'region_id': '4',
        'name_ar': 'جرادة',
        'name_fr': 'Jerada',
        'name_en': 'Jerada'
      },
      {
        'id': '16',
        'region_id': '4',
        'name_ar': 'فجيج',
        'name_fr': 'Figuig',
        'name_en': 'Figuig'
      },
      {
        'id': '17',
        'region_id': '4',
        'name_ar': 'دريوش',
        'name_fr': 'Driouch',
        'name_en': 'Driouch'
      },
      {
        'id': '18',
        'region_id': '4',
        'name_ar': 'العروي',
        'name_fr': 'Al Aroui',
        'name_en': 'Al Aroui'
      },
      {
        'id': '19',
        'region_id': '4',
        'name_ar': 'زايو',
        'name_fr': 'Zaio',
        'name_en': 'Zaio'
      },
      {
        'id': '20',
        'region_id': '4',
        'name_ar': 'أحفير',
        'name_fr': 'Ahfir',
        'name_en': 'Ahfir'
      },

      // جهة العيون الساقية الحمراء
      {
        'id': '21',
        'region_id': '5',
        'name_ar': 'العيون',
        'name_fr': 'Laâyoune',
        'name_en': 'Laayoune'
      },
      {
        'id': '22',
        'region_id': '5',
        'name_ar': 'بوجدور',
        'name_fr': 'Boujdour',
        'name_en': 'Boujdour'
      },
      {
        'id': '23',
        'region_id': '5',
        'name_ar': 'طرفاية',
        'name_fr': 'Tarfaya',
        'name_en': 'Tarfaya'
      },
      {
        'id': '24',
        'region_id': '5',
        'name_ar': 'السمارة',
        'name_fr': 'Es-Semara',
        'name_en': 'Es-Semara'
      },

      // جهة بني ملال خنيفرة
      {
        'id': '25',
        'region_id': '6',
        'name_ar': 'بني ملال',
        'name_fr': 'Béni Mellal',
        'name_en': 'Beni Mellal'
      },
      {
        'id': '26',
        'region_id': '6',
        'name_ar': 'خنيفرة',
        'name_fr': 'Khénifra',
        'name_en': 'Khenifra'
      },
      {
        'id': '27',
        'region_id': '6',
        'name_ar': 'خريبكة',
        'name_fr': 'Khouribga',
        'name_en': 'Khouribga'
      },
      {
        'id': '28',
        'region_id': '6',
        'name_ar': 'أزيلال',
        'name_fr': 'Azilal',
        'name_en': 'Azilal'
      },

      // جهة مراكش آسفي
      {
        'id': '29',
        'region_id': '7',
        'name_ar': 'مراكش',
        'name_fr': 'Marrakech',
        'name_en': 'Marrakech'
      },
      {
        'id': '30',
        'region_id': '7',
        'name_ar': 'آسفي',
        'name_fr': 'Safi',
        'name_en': 'Safi'
      },
      {
        'id': '31',
        'region_id': '7',
        'name_ar': 'الصويرة',
        'name_fr': 'Essaouira',
        'name_en': 'Essaouira'
      },
      {
        'id': '32',
        'region_id': '7',
        'name_ar': 'قلعة السراغنة',
        'name_fr': 'Kelâa des Sraghna',
        'name_en': 'Kelaa des Sraghna'
      },

      // جهة درعة تافيلالت
      {
        'id': '33',
        'region_id': '8',
        'name_ar': 'الرشيدية',
        'name_fr': 'Errachidia',
        'name_en': 'Errachidia'
      },
      {
        'id': '34',
        'region_id': '8',
        'name_ar': 'ورزازات',
        'name_fr': 'Ouarzazate',
        'name_en': 'Ouarzazate'
      },
      {
        'id': '35',
        'region_id': '8',
        'name_ar': 'زاكورة',
        'name_fr': 'Zagora',
        'name_en': 'Zagora'
      },
      {
        'id': '36',
        'region_id': '8',
        'name_ar': 'تنغير',
        'name_fr': 'Tinghir',
        'name_en': 'Tinghir'
      },

      // جهة سوس ماسة
      {
        'id': '37',
        'region_id': '9',
        'name_ar': 'أكادير',
        'name_fr': 'Agadir',
        'name_en': 'Agadir'
      },
      {
        'id': '38',
        'region_id': '9',
        'name_ar': 'إنزكان',
        'name_fr': 'Inezgane',
        'name_en': 'Inezgane'
      },
      {
        'id': '39',
        'region_id': '9',
        'name_ar': 'تارودانت',
        'name_fr': 'Taroudant',
        'name_en': 'Taroudant'
      },
      {
        'id': '40',
        'region_id': '9',
        'name_ar': 'تيزنيت',
        'name_fr': 'Tiznit',
        'name_en': 'Tiznit'
      },

      // جهة كلميم واد نون
      {
        'id': '41',
        'region_id': '10',
        'name_ar': 'كلميم',
        'name_fr': 'Guelmim',
        'name_en': 'Guelmim'
      },
      {
        'id': '42',
        'region_id': '10',
        'name_ar': 'طانطان',
        'name_fr': 'Tan-Tan',
        'name_en': 'Tan-Tan'
      },
      {
        'id': '43',
        'region_id': '10',
        'name_ar': 'سيدي إفني',
        'name_fr': 'Sidi Ifni',
        'name_en': 'Sidi Ifni'
      },

      // جهة فاس مكناس
      {
        'id': '44',
        'region_id': '11',
        'name_ar': 'فاس',
        'name_fr': 'Fès',
        'name_en': 'Fes'
      },
      {
        'id': '45',
        'region_id': '11',
        'name_ar': 'مكناس',
        'name_fr': 'Meknès',
        'name_en': 'Meknes'
      },
      {
        'id': '46',
        'region_id': '11',
        'name_ar': 'تازة',
        'name_fr': 'Taza',
        'name_en': 'Taza'
      },
      {
        'id': '47',
        'region_id': '11',
        'name_ar': 'صفرو',
        'name_fr': 'Sefrou',
        'name_en': 'Sefrou'
      },

      // جهة طنجة تطوان الحسيمة
      {
        'id': '48',
        'region_id': '12',
        'name_ar': 'طنجة',
        'name_fr': 'Tanger',
        'name_en': 'Tangier'
      },
      {
        'id': '49',
        'region_id': '12',
        'name_ar': 'تطوان',
        'name_fr': 'Tétouan',
        'name_en': 'Tetouan'
      },
      {
        'id': '50',
        'region_id': '12',
        'name_ar': 'الحسيمة',
        'name_fr': 'Al Hoceïma',
        'name_en': 'Al Hoceima'
      },

      // مدن إضافية لتغطية المزيد من المعرفات
      {
        'id': '51',
        'region_id': '1',
        'name_ar': 'لكويرة',
        'name_fr': 'Lagouira',
        'name_en': 'Lagouira'
      },
      {
        'id': '52',
        'region_id': '2',
        'name_ar': 'النواصر',
        'name_fr': 'Nouaceur',
        'name_en': 'Nouaceur'
      },
      {
        'id': '53',
        'region_id': '2',
        'name_ar': 'مديونة',
        'name_fr': 'Mediouna',
        'name_en': 'Mediouna'
      },
      {
        'id': '54',
        'region_id': '3',
        'name_ar': 'الخميسات',
        'name_fr': 'Khémisset',
        'name_en': 'Khemisset'
      },
      {
        'id': '55',
        'region_id': '3',
        'name_ar': 'سيدي قاسم',
        'name_fr': 'Sidi Kacem',
        'name_en': 'Sidi Kacem'
      },
      {
        'id': '56',
        'region_id': '3',
        'name_ar': 'سيدي سليمان',
        'name_fr': 'Sidi Slimane',
        'name_en': 'Sidi Slimane'
      },
      {
        'id': '57',
        'region_id': '4',
        'name_ar': 'بوعرفة',
        'name_fr': 'Bouarfa',
        'name_en': 'Bouarfa'
      },
      {
        'id': '58',
        'region_id': '4',
        'name_ar': 'العيون سيدي ملوك',
        'name_fr': 'Aïn Sidi Mellouk',
        'name_en': 'Ain Sidi Mellouk'
      },
      {
        'id': '59',
        'region_id': '4',
        'name_ar': 'تندرارة',
        'name_fr': 'Tendrara',
        'name_en': 'Tendrara'
      },
      {
        'id': '60',
        'region_id': '4',
        'name_ar': 'بني تاجيت',
        'name_fr': 'Beni Tajjit',
        'name_en': 'Beni Tajjit'
      },
      {
        'id': '61',
        'region_id': '5',
        'name_ar': 'المرسى',
        'name_fr': 'El Marsa',
        'name_en': 'El Marsa'
      },
      {
        'id': '62',
        'region_id': '5',
        'name_ar': 'الكركرات',
        'name_fr': 'Guerguerat',
        'name_en': 'Guerguerat'
      },
      {
        'id': '63',
        'region_id': '6',
        'name_ar': 'الفقيه بن صالح',
        'name_fr': 'Fquih Ben Salah',
        'name_en': 'Fquih Ben Salah'
      },
      {
        'id': '64',
        'region_id': '6',
        'name_ar': 'قصبة تادلة',
        'name_fr': 'Kasba Tadla',
        'name_en': 'Kasba Tadla'
      },
      {
        'id': '65',
        'region_id': '7',
        'name_ar': 'شيشاوة',
        'name_fr': 'Chichaoua',
        'name_en': 'Chichaoua'
      },
      {
        'id': '66',
        'region_id': '7',
        'name_ar': 'الحوز',
        'name_fr': 'Al Haouz',
        'name_en': 'Al Haouz'
      },
      {
        'id': '67',
        'region_id': '8',
        'name_ar': 'ميدلت',
        'name_fr': 'Midelt',
        'name_en': 'Midelt'
      },
      {
        'id': '68',
        'region_id': '8',
        'name_ar': 'الريش',
        'name_fr': 'Arfoud',
        'name_en': 'Arfoud'
      },
      {
        'id': '69',
        'region_id': '9',
        'name_ar': 'شتوكة أيت باها',
        'name_fr': 'Chtouka Ait Baha',
        'name_en': 'Chtouka Ait Baha'
      },
      {
        'id': '70',
        'region_id': '9',
        'name_ar': 'اولاد تايمة',
        'name_fr': 'Ouled Teima',
        'name_en': 'Ouled Teima'
      },
      {
        'id': '71',
        'region_id': '10',
        'name_ar': 'أسا الزاك',
        'name_fr': 'Assa-Zag',
        'name_en': 'Assa-Zag'
      },
      {
        'id': '72',
        'region_id': '11',
        'name_ar': 'إفران',
        'name_fr': 'Ifrane',
        'name_en': 'Ifrane'
      },
      {
        'id': '73',
        'region_id': '11',
        'name_ar': 'الحاجب',
        'name_fr': 'El Hajeb',
        'name_en': 'El Hajeb'
      },
      {
        'id': '74',
        'region_id': '12',
        'name_ar': 'شفشاون',
        'name_fr': 'Chefchaouen',
        'name_en': 'Chefchaouen'
      },
      {
        'id': '75',
        'region_id': '12',
        'name_ar': 'العرائش',
        'name_fr': 'Larache',
        'name_en': 'Larache'
      },
      {
        'id': '76',
        'region_id': '12',
        'name_ar': 'أصيلة',
        'name_fr': 'Asilah',
        'name_en': 'Asilah'
      },
      {
        'id': '77',
        'region_id': '1',
        'name_ar': 'بئر كندوز',
        'name_fr': 'Bir Gandouz',
        'name_en': 'Bir Gandouz'
      },
      {
        'id': '78',
        'region_id': '2',
        'name_ar': 'بنسليمان',
        'name_fr': 'Benslimane',
        'name_en': 'Benslimane'
      },
      {
        'id': '79',
        'region_id': '3',
        'name_ar': 'المعمورة',
        'name_fr': 'Mehdia',
        'name_en': 'Mehdia'
      },
      {
        'id': '80',
        'region_id': '4',
        'name_ar': 'السعيدية',
        'name_fr': 'Saïdia',
        'name_en': 'Saidia'
      },
      {
        'id': '81',
        'region_id': '4',
        'name_ar': 'رأس الماء',
        'name_fr': 'Ras El Ma',
        'name_en': 'Ras El Ma'
      },
      {
        'id': '82',
        'region_id': '4',
        'name_ar': 'عين بني مطهر',
        'name_fr': 'Ain Beni Mathar',
        'name_en': 'Ain Beni Mathar'
      },

      // مدن إضافية لتغطية المعرفات العالية
      {
        'id': '83',
        'region_id': '5',
        'name_ar': 'فم الواد',
        'name_fr': 'Foum El Oued',
        'name_en': 'Foum El Oued'
      },
      {
        'id': '84',
        'region_id': '6',
        'name_ar': 'وادي زم',
        'name_fr': 'Oued Zem',
        'name_en': 'Oued Zem'
      },
      {
        'id': '85',
        'region_id': '7',
        'name_ar': 'أمزميز',
        'name_fr': 'Amizmiz',
        'name_en': 'Amizmiz'
      },
      {
        'id': '86',
        'region_id': '8',
        'name_ar': 'بومالن دادس',
        'name_fr': 'Boumalne Dades',
        'name_en': 'Boumalne Dades'
      },
      {
        'id': '87',
        'region_id': '9',
        'name_ar': 'بيوكرى',
        'name_fr': 'Biougra',
        'name_en': 'Biougra'
      },
      {
        'id': '88',
        'region_id': '10',
        'name_ar': 'أقا',
        'name_fr': 'Akka',
        'name_en': 'Akka'
      },
      {
        'id': '89',
        'region_id': '11',
        'name_ar': 'بولمان',
        'name_fr': 'Boulemane',
        'name_en': 'Boulemane'
      },
      {
        'id': '90',
        'region_id': '12',
        'name_ar': 'وزان',
        'name_fr': 'Ouazzane',
        'name_en': 'Ouazzane'
      },

      // مدن إضافية لتغطية المعرفات العالية حتى 200
      {
        'id': '91',
        'region_id': '1',
        'name_ar': 'ميجك',
        'name_fr': 'Mijik',
        'name_en': 'Mijik'
      },
      {
        'id': '92',
        'region_id': '2',
        'name_ar': 'دار بوعزة',
        'name_fr': 'Dar Bouazza',
        'name_en': 'Dar Bouazza'
      },
      {
        'id': '93',
        'region_id': '3',
        'name_ar': 'عين عتيق',
        'name_fr': 'Ain Atiq',
        'name_en': 'Ain Atiq'
      },
      {
        'id': '94',
        'region_id': '4',
        'name_ar': 'مداغ',
        'name_fr': 'Madagh',
        'name_en': 'Madagh'
      },
      {
        'id': '95',
        'region_id': '5',
        'name_ar': 'الحقونية',
        'name_fr': 'Haounia',
        'name_en': 'Haounia'
      },
      {
        'id': '96',
        'region_id': '6',
        'name_ar': 'دير القصيبة',
        'name_fr': 'Dir El Ksiba',
        'name_en': 'Dir El Ksiba'
      },
      {
        'id': '97',
        'region_id': '7',
        'name_ar': 'تحناوت',
        'name_fr': 'Tahannaout',
        'name_en': 'Tahannaout'
      },
      {
        'id': '98',
        'region_id': '8',
        'name_ar': 'كلميمة',
        'name_fr': 'Goulmima',
        'name_en': 'Goulmima'
      },
      {
        'id': '99',
        'region_id': '9',
        'name_ar': 'الدشيرة الجهادية',
        'name_fr': 'Dcheira El Jihadia',
        'name_en': 'Dcheira El Jihadia'
      },
      {
        'id': '100',
        'region_id': '10',
        'name_ar': 'فاصك',
        'name_fr': 'Fask',
        'name_en': 'Fask'
      }
    ];

    // إضافة مدن إضافية لتغطية المعرفات العالية (101-200)
    for (int i = 101; i <= 200; i++) {
      final regionId = ((i - 1) % 12) + 1; // توزيع على الجهات الـ12
      cities.add({
        'id': i.toString(),
        'region_id': regionId.toString(),
        'name_ar': 'مدينة $i',
        'name_fr': 'Ville $i',
        'name_en': 'City $i'
      });
    }

    for (final city in cities) {
      await db.insert('cities', {
        ...city,
        'created_at': DateTime.now().toIso8601String(),
      });
    }
  }

  /// إدراج الحرف
  Future<void> _insertCrafts(Database db) async {
    final crafts = [
      {
        'id': 1,
        'name_ar': 'أجزاء الدراجات',
        'name_fr': 'Pièces de vélos',
        'name_en': 'Bicycle Parts'
      },
      {
        'id': 2,
        'name_ar': 'أجزاء السيارات',
        'name_fr': 'Pièces automobiles',
        'name_en': 'Car Parts'
      },
      {
        'id': 3,
        'name_ar': 'أحذية الرجال',
        'name_fr': 'Chaussures hommes',
        'name_en': 'Men\'s Shoes'
      },
      {
        'id': 4,
        'name_ar': 'أحذية النساء',
        'name_fr': 'Chaussures femmes',
        'name_en': 'Women\'s Shoes'
      },
      {
        'id': 5,
        'name_ar': 'أعمال البستنة',
        'name_fr': 'Jardinage',
        'name_en': 'Gardening'
      },
      {
        'id': 6,
        'name_ar': 'أعمال البناء',
        'name_fr': 'Construction',
        'name_en': 'Construction'
      },
      {
        'id': 7,
        'name_ar': 'أعمال الترجمة',
        'name_fr': 'Traduction',
        'name_en': 'Translation'
      },
      {
        'id': 8,
        'name_ar': 'أعمال التصوير',
        'name_fr': 'Photographie',
        'name_en': 'Photography'
      },
      {
        'id': 9,
        'name_ar': 'أعمال الجبس',
        'name_fr': 'Plâtrerie',
        'name_en': 'Plastering'
      },
      {
        'id': 10,
        'name_ar': 'أعمال الحدادة',
        'name_fr': 'Ferronnerie',
        'name_en': 'Blacksmithing'
      },
      {
        'id': 11,
        'name_ar': 'أعمال الخزف',
        'name_fr': 'Céramique',
        'name_en': 'Ceramics'
      },
      {
        'id': 12,
        'name_ar': 'أعمال الخياطة',
        'name_fr': 'Couture',
        'name_en': 'Sewing'
      },
      {
        'id': 13,
        'name_ar': 'أعمال الرخام',
        'name_fr': 'Marbrerie',
        'name_en': 'Marble Work'
      },
      {
        'id': 14,
        'name_ar': 'أعمال الرسم',
        'name_fr': 'Peinture',
        'name_en': 'Painting'
      },
      {
        'id': 15,
        'name_ar': 'أعمال الزراعة',
        'name_fr': 'Agriculture',
        'name_en': 'Agriculture'
      },
      {
        'id': 16,
        'name_ar': 'أعمال الزليج',
        'name_fr': 'Zellige',
        'name_en': 'Zellige'
      },
      {
        'id': 17,
        'name_ar': 'أعمال السباكة',
        'name_fr': 'Plomberie',
        'name_en': 'Plumbing'
      },
      {
        'id': 18,
        'name_ar': 'أعمال الصباغة',
        'name_fr': 'Teinture',
        'name_en': 'Dyeing'
      },
      {
        'id': 19,
        'name_ar': 'أعمال الطبخ',
        'name_fr': 'Cuisine',
        'name_en': 'Cooking'
      },
      {
        'id': 20,
        'name_ar': 'أعمال المحاسبة',
        'name_fr': 'Comptabilité',
        'name_en': 'Accounting'
      },
      {
        'id': 21,
        'name_ar': 'أعمال الميكانيك',
        'name_fr': 'Mécanique',
        'name_en': 'Mechanics'
      },
      {
        'id': 22,
        'name_ar': 'أعمال النظافة',
        'name_fr': 'Nettoyage',
        'name_en': 'Cleaning'
      },
      {
        'id': 23,
        'name_ar': 'إدارة الحسابات',
        'name_fr': 'Gestion comptable',
        'name_en': 'Account Management'
      },
      {
        'id': 24,
        'name_ar': 'إدارة المشاريع',
        'name_fr': 'Gestion de projets',
        'name_en': 'Project Management'
      },
      {
        'id': 25,
        'name_ar': 'إصلاح الأحذية',
        'name_fr': 'Réparation chaussures',
        'name_en': 'Shoe Repair'
      },
      {
        'id': 26,
        'name_ar': 'إصلاح الأواني',
        'name_fr': 'Réparation ustensiles',
        'name_en': 'Utensil Repair'
      },
      {
        'id': 27,
        'name_ar': 'إصلاح التلفاز',
        'name_fr': 'Réparation TV',
        'name_en': 'TV Repair'
      },
      {
        'id': 28,
        'name_ar': 'إصلاح الحاسوب',
        'name_fr': 'Réparation ordinateur',
        'name_en': 'Computer Repair'
      },
      {
        'id': 29,
        'name_ar': 'إصلاح الدراجات',
        'name_fr': 'Réparation vélos',
        'name_en': 'Bicycle Repair'
      },
      {
        'id': 30,
        'name_ar': 'إصلاح الساعات',
        'name_fr': 'Réparation montres',
        'name_en': 'Watch Repair'
      },
    ];

    for (final craft in crafts) {
      await db.insert('crafts', {
        ...craft,
        'created_at': DateTime.now().toIso8601String(),
      });
    }
  }

  /// إغلاق قاعدة البيانات
  Future<void> close() async {
    final db = _database;
    if (db != null) {
      await db.close();
      _database = null;
      debugPrint('🔒 تم إغلاق قاعدة البيانات');
    }
  }

  /// حذف قاعدة البيانات (للاختبار فقط)
  Future<void> deleteDatabase() async {
    try {
      // إغلاق قاعدة البيانات أولاً
      await close();

      // الحصول على مسار قاعدة البيانات
      final databasesPath = await getDatabasesPath();
      final path = join(databasesPath, _databaseName);

      // حذف ملف قاعدة البيانات
      await databaseFactory.deleteDatabase(path);

      debugPrint('🗑️ تم حذف قاعدة البيانات: $path');
    } catch (e) {
      debugPrint('❌ خطأ في حذف قاعدة البيانات: $e');
      rethrow;
    }
  }
}
