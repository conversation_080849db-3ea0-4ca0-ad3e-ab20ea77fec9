import 'package:flutter/material.dart';
import '../../shared/themes/app_theme.dart';
import '../../core/repositories/notification_repository.dart';
import '../../core/services/auth_service.dart';
import '../../core/models/notification_model.dart';

/// صفحة الإشعارات للحرفي
class CraftsmanNotificationsScreen extends StatefulWidget {
  const CraftsmanNotificationsScreen({super.key});

  @override
  State<CraftsmanNotificationsScreen> createState() =>
      _CraftsmanNotificationsScreenState();
}

class _CraftsmanNotificationsScreenState
    extends State<CraftsmanNotificationsScreen> {
  final NotificationRepository _notificationRepository =
      NotificationRepository();
  final AuthService _authService = AuthService();

  List<NotificationModel> _notifications = [];
  bool _isLoading = true;
  bool _isMarkingAllRead = false;
  bool _isDeletingAll = false;
  int _unreadCount = 0;

  @override
  void initState() {
    super.initState();
    _loadNotifications();
  }

  /// تحميل الإشعارات
  Future<void> _loadNotifications() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final currentUser = await _authService.getCurrentUser();
      if (currentUser == null) {
        debugPrint('⚠️ لا يوجد مستخدم مسجل');
        setState(() {
          _isLoading = false;
        });
        return;
      }

      // جلب الإشعارات والعدد غير المقروء
      final results = await Future.wait([
        _notificationRepository
            .getUserNotifications(currentUser.userId.toString()),
        _notificationRepository
            .getUnreadNotificationsCount(currentUser.userId.toString()),
      ]);

      _notifications = results[0] as List<NotificationModel>;
      _unreadCount = results[1] as int;

      setState(() {
        _isLoading = false;
      });

      debugPrint('✅ تم تحميل ${_notifications.length} إشعار للحرفي');
    } catch (e) {
      debugPrint('❌ خطأ في تحميل الإشعارات: $e');
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل الإشعارات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// تحديد إشعار كمقروء
  Future<void> _markAsRead(NotificationModel notification) async {
    if (notification.isRead) return;

    try {
      final success = await _notificationRepository.markAsRead(notification.id);
      if (success) {
        setState(() {
          final index =
              _notifications.indexWhere((n) => n.id == notification.id);
          if (index != -1) {
            _notifications[index] = notification.copyWith(isRead: true);
            _unreadCount = (_unreadCount - 1).clamp(0, _unreadCount);
          }
        });
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحديد الإشعار كمقروء: $e');
    }
  }

  /// تحديد جميع الإشعارات كمقروءة
  Future<void> _markAllAsRead() async {
    final currentUser = await _authService.getCurrentUser();
    if (currentUser == null) return;

    setState(() {
      _isMarkingAllRead = true;
    });

    try {
      final success = await _notificationRepository
          .markAllAsRead(currentUser.userId.toString());
      if (success) {
        setState(() {
          _notifications =
              _notifications.map((n) => n.copyWith(isRead: true)).toList();
          _unreadCount = 0;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم تحديد جميع الإشعارات كمقروءة'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحديد جميع الإشعارات كمقروءة: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحديد الإشعارات كمقروءة: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isMarkingAllRead = false;
      });
    }
  }

  /// حذف جميع الإشعارات
  Future<void> _deleteAllNotifications() async {
    final currentUser = await _authService.getCurrentUser();
    if (currentUser == null) return;

    // تأكيد الحذف
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text(
            'هل أنت متأكد من حذف جميع الإشعارات؟ لا يمكن التراجع عن هذا الإجراء.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    setState(() {
      _isDeletingAll = true;
    });

    try {
      final success = await _notificationRepository
          .deleteAllUserNotifications(currentUser.userId.toString());
      if (success) {
        setState(() {
          _notifications.clear();
          _unreadCount = 0;
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم حذف جميع الإشعارات'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('❌ خطأ في حذف جميع الإشعارات: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حذف الإشعارات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isDeletingAll = false;
      });
    }
  }

  /// حذف إشعار واحد
  Future<void> _deleteNotification(NotificationModel notification) async {
    try {
      final success =
          await _notificationRepository.deleteNotification(notification.id);
      if (success) {
        setState(() {
          _notifications.removeWhere((n) => n.id == notification.id);
          if (!notification.isRead) {
            _unreadCount = (_unreadCount - 1).clamp(0, _unreadCount);
          }
        });

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم حذف الإشعار'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('❌ خطأ في حذف الإشعار: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حذف الإشعار: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          'الإشعارات',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppTheme.primaryColor,
        elevation: 0,
        actions: [
          // زر تحديد الكل كمقروء
          if (_notifications.any((n) => !n.isRead))
            IconButton(
              icon: _isMarkingAllRead
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Icon(Icons.done_all, color: Colors.white),
              onPressed: _isMarkingAllRead ? null : _markAllAsRead,
              tooltip: 'تحديد الكل كمقروء',
            ),
          // زر حذف الكل
          if (_notifications.isNotEmpty)
            IconButton(
              icon: _isDeletingAll
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Icon(Icons.delete_sweep, color: Colors.white),
              onPressed: _isDeletingAll ? null : _deleteAllNotifications,
              tooltip: 'حذف جميع الإشعارات',
            ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_notifications.isEmpty) {
      return _buildEmptyState();
    }

    return RefreshIndicator(
      onRefresh: _loadNotifications,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _notifications.length,
        itemBuilder: (context, index) {
          final notification = _notifications[index];
          return _buildNotificationCard(notification);
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.notifications_off,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد إشعارات',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'ستظهر إشعاراتك هنا عند وصولها',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationCard(NotificationModel notification) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: notification.isRead
            ? null
            : Border.all(
                color: AppTheme.primaryColor.withOpacity(0.3), width: 1),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 6,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Dismissible(
        key: Key(notification.id),
        direction: DismissDirection.endToStart,
        background: Container(
          alignment: Alignment.centerRight,
          padding: const EdgeInsets.only(right: 20),
          decoration: BoxDecoration(
            color: Colors.red,
            borderRadius: BorderRadius.circular(12),
          ),
          child: const Icon(
            Icons.delete,
            color: Colors.white,
            size: 24,
          ),
        ),
        onDismissed: (direction) {
          _deleteNotification(notification);
        },
        child: ListTile(
          contentPadding: const EdgeInsets.all(16),
          leading: Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: notification.type.color.withOpacity(0.1),
              borderRadius: BorderRadius.circular(25),
            ),
            child: Icon(
              notification.type.icon,
              color: notification.type.color,
              size: 24,
            ),
          ),
          title: Text(
            notification.title,
            style: TextStyle(
              fontWeight:
                  notification.isRead ? FontWeight.normal : FontWeight.bold,
              fontSize: 16,
              color: notification.isRead ? Colors.grey[700] : Colors.black,
            ),
          ),
          subtitle: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 4),
              Text(
                notification.message,
                style: TextStyle(
                  fontSize: 14,
                  color:
                      notification.isRead ? Colors.grey[600] : Colors.grey[800],
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 8),
              Text(
                _formatDateTime(notification.createdAt),
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[500],
                ),
              ),
            ],
          ),
          trailing: notification.isRead
              ? null
              : Container(
                  width: 8,
                  height: 8,
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
          onTap: () => _markAsRead(notification),
        ),
      ),
    );
  }

  /// تنسيق التاريخ والوقت بالعربية
  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} يوم';
    } else {
      return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
    }
  }
}
