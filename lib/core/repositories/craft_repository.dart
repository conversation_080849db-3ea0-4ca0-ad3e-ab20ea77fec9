import '../database/database_helper.dart';
import '../utils/logger.dart';
import '../models/craft_model.dart';

/// مستودع الحرف
class CraftRepository {
  final DatabaseHelper _dbHelper = DatabaseHelper();

  /// الحصول على جميع الحرف
  Future<List<CraftModel>> getAllCrafts() async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'crafts',
        orderBy: 'name_ar ASC',
      );

      return maps.map((map) => CraftModel.fromMap(map)).toList();
    } catch (e) {
      AppLogger.info('خطأ في الحصول على الحرف: $e');
      return [];
    }
  }

  /// الحصول على حرفة بالمعرف
  Future<CraftModel?> getCraftById(String craftId) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'crafts',
        where: 'id = ?',
        whereArgs: [craftId],
        limit: 1,
      );

      if (maps.isEmpty) return null;
      return CraftModel.fromMap(maps.first);
    } catch (e) {
      AppLogger.info('خطأ في الحصول على الحرفة: $e');
      return null;
    }
  }

  /// البحث في الحرف
  Future<List<CraftModel>> searchCrafts(String query) async {
    try {
      final db = await _dbHelper.database;
      final searchQuery = '%$query%';

      final List<Map<String, dynamic>> maps = await db.query(
        'crafts',
        where: 'name_ar LIKE ? OR name_en LIKE ? OR name_fr LIKE ?',
        whereArgs: [searchQuery, searchQuery, searchQuery],
        orderBy: 'name_ar ASC',
      );

      return maps.map((map) => CraftModel.fromMap(map)).toList();
    } catch (e) {
      AppLogger.info('خطأ في البحث في الحرف: $e');
      return [];
    }
  }

  /// إضافة حرفة جديدة
  Future<bool> addCraft(CraftModel craft) async {
    try {
      final db = await _dbHelper.database;
      final result = await db.insert('crafts', craft.toMap());
      return result > 0;
    } catch (e) {
      AppLogger.info('خطأ في إضافة الحرفة: $e');
      return false;
    }
  }

  /// تحديث حرفة
  Future<bool> updateCraft(CraftModel craft) async {
    try {
      final db = await _dbHelper.database;
      final result = await db.update(
        'crafts',
        craft.toMap(),
        where: 'id = ?',
        whereArgs: [craft.id],
      );
      return result > 0;
    } catch (e) {
      AppLogger.info('خطأ في تحديث الحرفة: $e');
      return false;
    }
  }

  /// حذف حرفة
  Future<bool> deleteCraft(String craftId) async {
    try {
      final db = await _dbHelper.database;
      final result = await db.delete(
        'crafts',
        where: 'id = ?',
        whereArgs: [craftId],
      );
      return result > 0;
    } catch (e) {
      AppLogger.info('خطأ في حذف الحرفة: $e');
      return false;
    }
  }

  /// الحصول على الحرف الأكثر طلباً
  Future<List<CraftModel>> getPopularCrafts({int limit = 10}) async {
    try {
      final db = await _dbHelper.database;
      
      // استعلام للحصول على الحرف الأكثر طلباً
      final List<Map<String, dynamic>> maps = await db.rawQuery('''
        SELECT c.*, COUNT(o.id) as order_count
        FROM crafts c
        LEFT JOIN orders o ON c.id = o.craft_id
        GROUP BY c.id
        ORDER BY order_count DESC, c.name_ar ASC
        LIMIT ?
      ''', [limit]);

      return maps.map((map) => CraftModel.fromMap(map)).toList();
    } catch (e) {
      AppLogger.info('خطأ في الحصول على الحرف الشائعة: $e');
      // في حالة الخطأ، نعيد جميع الحرف
      return getAllCrafts();
    }
  }

  /// الحصول على إحصائيات الحرف
  Future<Map<String, dynamic>> getCraftsStatistics() async {
    try {
      final db = await _dbHelper.database;

      // عدد الحرف الإجمالي
      final totalCrafts = await db.rawQuery('SELECT COUNT(*) as count FROM crafts');

      // عدد الحرفيين لكل حرفة
      final craftsmenPerCraft = await db.rawQuery('''
        SELECT c.name_ar as craft_name, COUNT(cr.id) as craftsmen_count
        FROM crafts c
        LEFT JOIN craftsmen cr ON c.id = cr.craft_id
        GROUP BY c.id, c.name_ar
        ORDER BY craftsmen_count DESC
      ''');

      // عدد الطلبات لكل حرفة
      final ordersPerCraft = await db.rawQuery('''
        SELECT c.name_ar as craft_name, COUNT(o.id) as orders_count
        FROM crafts c
        LEFT JOIN orders o ON c.id = o.craft_id
        GROUP BY c.id, c.name_ar
        ORDER BY orders_count DESC
      ''');

      return {
        'total_crafts': totalCrafts.first['count'],
        'craftsmen_per_craft': craftsmenPerCraft,
        'orders_per_craft': ordersPerCraft,
      };
    } catch (e) {
      AppLogger.info('خطأ في الحصول على إحصائيات الحرف: $e');
      return {};
    }
  }

  /// الحصول على الحرف المتاحة في منطقة معينة
  Future<List<CraftModel>> getCraftsByRegion(String regionId) async {
    try {
      final db = await _dbHelper.database;
      
      final List<Map<String, dynamic>> maps = await db.rawQuery('''
        SELECT DISTINCT c.*
        FROM crafts c
        INNER JOIN craftsmen cr ON c.id = cr.craft_id
        INNER JOIN cities ci ON cr.city_id = ci.id
        WHERE ci.region_id = ?
        ORDER BY c.name_ar ASC
      ''', [regionId]);

      return maps.map((map) => CraftModel.fromMap(map)).toList();
    } catch (e) {
      AppLogger.info('خطأ في الحصول على حرف المنطقة: $e');
      return [];
    }
  }

  /// الحصول على الحرف المتاحة في مدينة معينة
  Future<List<CraftModel>> getCraftsByCity(String cityId) async {
    try {
      final db = await _dbHelper.database;
      
      final List<Map<String, dynamic>> maps = await db.rawQuery('''
        SELECT DISTINCT c.*
        FROM crafts c
        INNER JOIN craftsmen cr ON c.id = cr.craft_id
        WHERE cr.city_id = ?
        ORDER BY c.name_ar ASC
      ''', [cityId]);

      return maps.map((map) => CraftModel.fromMap(map)).toList();
    } catch (e) {
      AppLogger.info('خطأ في الحصول على حرف المدينة: $e');
      return [];
    }
  }

  /// البحث المتقدم في الحرف
  Future<List<CraftModel>> advancedSearchCrafts({
    String? query,
    String? regionId,
    String? cityId,
  }) async {
    try {
      final db = await _dbHelper.database;

      String whereClause = '1 = 1';
      List<dynamic> whereArgs = [];

      if (query != null && query.isNotEmpty) {
        whereClause += ' AND (c.name_ar LIKE ? OR c.name_en LIKE ? OR c.name_fr LIKE ?)';
        final searchQuery = '%$query%';
        whereArgs.addAll([searchQuery, searchQuery, searchQuery]);
      }

      if (cityId != null) {
        whereClause += ' AND cr.city_id = ?';
        whereArgs.add(cityId);
      } else if (regionId != null) {
        whereClause += ' AND ci.region_id = ?';
        whereArgs.add(regionId);
      }

      final List<Map<String, dynamic>> maps = await db.rawQuery('''
        SELECT DISTINCT c.*
        FROM crafts c
        INNER JOIN craftsmen cr ON c.id = cr.craft_id
        INNER JOIN cities ci ON cr.city_id = ci.id
        WHERE $whereClause
        ORDER BY c.name_ar ASC
      ''', whereArgs);

      return maps.map((map) => CraftModel.fromMap(map)).toList();
    } catch (e) {
      AppLogger.info('خطأ في البحث المتقدم: $e');
      return [];
    }
  }

  /// تحديث عداد الطلبات للحرفة
  Future<bool> incrementCraftOrderCount(String craftId) async {
    try {
      final db = await _dbHelper.database;
      
      // هذا مجرد مثال - يمكن تنفيذه بطرق مختلفة حسب هيكل قاعدة البيانات
      final result = await db.rawUpdate('''
        UPDATE crafts 
        SET order_count = COALESCE(order_count, 0) + 1 
        WHERE id = ?
      ''', [craftId]);
      
      return result > 0;
    } catch (e) {
      AppLogger.info('خطأ في تحديث عداد الطلبات: $e');
      return false;
    }
  }

  /// الحصول على عدد الحرفيين لكل حرفة
  Future<Map<String, int>> getCraftsmenCountPerCraft() async {
    try {
      final db = await _dbHelper.database;
      
      final List<Map<String, dynamic>> maps = await db.rawQuery('''
        SELECT c.id, c.name_ar, COUNT(cr.id) as craftsmen_count
        FROM crafts c
        LEFT JOIN craftsmen cr ON c.id = cr.craft_id
        GROUP BY c.id, c.name_ar
      ''');

      Map<String, int> result = {};
      for (var map in maps) {
        result[map['id']] = map['craftsmen_count'] as int;
      }
      
      return result;
    } catch (e) {
      AppLogger.info('خطأ في الحصول على عدد الحرفيين: $e');
      return {};
    }
  }
}
