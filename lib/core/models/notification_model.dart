import 'package:flutter/material.dart';

/// نموذج الإشعارات
class NotificationModel {
  final String id;
  final String title;
  final String message;
  final NotificationType type;
  final bool isRead;
  final DateTime createdAt;
  final String userId;
  final String? relatedId; // معرف الطلب أو العنصر المرتبط
  final Map<String, dynamic>? data; // بيانات إضافية

  const NotificationModel({
    required this.id,
    required this.title,
    required this.message,
    required this.type,
    required this.isRead,
    required this.createdAt,
    required this.userId,
    this.relatedId,
    this.data,
  });

  /// إنشاء نسخة من النموذج مع تحديث بعض الحقول
  NotificationModel copyWith({
    String? id,
    String? title,
    String? message,
    NotificationType? type,
    bool? isRead,
    DateTime? createdAt,
    String? userId,
    String? relatedId,
    Map<String, dynamic>? data,
  }) {
    return NotificationModel(
      id: id ?? this.id,
      title: title ?? this.title,
      message: message ?? this.message,
      type: type ?? this.type,
      isRead: isRead ?? this.isRead,
      createdAt: createdAt ?? this.createdAt,
      userId: userId ?? this.userId,
      relatedId: relatedId ?? this.relatedId,
      data: data ?? this.data,
    );
  }

  /// تحويل النموذج إلى Map لحفظه في قاعدة البيانات
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'message': message,
      'type': type.name,
      'is_read': isRead ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'user_id': userId,
      'related_id': relatedId,
      'data': data != null ? _mapToJson(data!) : null,
    };
  }

  /// إنشاء النموذج من Map قادم من قاعدة البيانات
  factory NotificationModel.fromMap(Map<String, dynamic> map) {
    return NotificationModel(
      id: map['id'] ?? '',
      title: map['title'] ?? '',
      message: map['message'] ?? '',
      type: NotificationType.fromString(map['type'] ?? 'info'),
      isRead: (map['is_read'] ?? 0) == 1,
      createdAt:
          DateTime.parse(map['created_at'] ?? DateTime.now().toIso8601String()),
      userId: map['user_id'] ?? '',
      relatedId: map['related_id'],
      data: map['data'] != null ? _jsonToMap(map['data']) : null,
    );
  }

  /// تحويل Map إلى JSON string
  static String _mapToJson(Map<String, dynamic> map) {
    // تحويل بسيط - يمكن استخدام dart:convert للتحويل المتقدم
    return map.toString();
  }

  /// تحويل JSON string إلى Map
  static Map<String, dynamic>? _jsonToMap(String json) {
    // تحويل بسيط - يمكن استخدام dart:convert للتحويل المتقدم
    try {
      return <String, dynamic>{};
    } catch (e) {
      return null;
    }
  }

  @override
  String toString() {
    return 'NotificationModel(id: $id, title: $title, type: $type, isRead: $isRead, createdAt: $createdAt)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NotificationModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// أنواع الإشعارات
enum NotificationType {
  // إشعارات الطلبات
  orderNew('order_new', 'طلب جديد'),
  orderAccepted('order_accepted', 'تم قبول الطلب'),
  orderRejected('order_rejected', 'تم رفض الطلب'),
  orderCompleted('order_completed', 'تم إكمال الطلب'),
  orderCancelled('order_cancelled', 'تم إلغاء الطلب'),

  // إشعارات الرسائل
  messageNew('message_new', 'رسالة جديدة'),

  // إشعارات التقييم
  ratingNew('rating_new', 'تقييم جديد'),

  // إشعارات التذكير
  reminder('reminder', 'تذكير'),

  // إشعارات عامة
  info('info', 'معلومات'),
  warning('warning', 'تحذير'),
  success('success', 'نجح'),
  error('error', 'خطأ');

  const NotificationType(this.value, this.displayName);

  final String value;
  final String displayName;

  /// إنشاء نوع الإشعار من النص
  static NotificationType fromString(String value) {
    for (NotificationType type in NotificationType.values) {
      if (type.value == value) {
        return type;
      }
    }
    return NotificationType.info;
  }

  /// الحصول على اسم النوع للحفظ في قاعدة البيانات
  String get name => value;
}

/// امتدادات مفيدة لأنواع الإشعارات
extension NotificationTypeExtension on NotificationType {
  /// الحصول على أيقونة مناسبة لنوع الإشعار
  IconData get icon {
    switch (this) {
      case NotificationType.orderNew:
        return Icons.assignment;
      case NotificationType.orderAccepted:
        return Icons.check_circle;
      case NotificationType.orderRejected:
        return Icons.cancel;
      case NotificationType.orderCompleted:
        return Icons.task_alt;
      case NotificationType.orderCancelled:
        return Icons.highlight_off;
      case NotificationType.messageNew:
        return Icons.message;
      case NotificationType.ratingNew:
        return Icons.star;
      case NotificationType.reminder:
        return Icons.schedule;
      case NotificationType.info:
        return Icons.info;
      case NotificationType.warning:
        return Icons.warning;
      case NotificationType.success:
        return Icons.check_circle_outline;
      case NotificationType.error:
        return Icons.error_outline;
    }
  }

  /// الحصول على لون مناسب لنوع الإشعار
  Color get color {
    switch (this) {
      case NotificationType.orderNew:
        return Colors.orange;
      case NotificationType.orderAccepted:
      case NotificationType.orderCompleted:
      case NotificationType.success:
        return Colors.green;
      case NotificationType.orderRejected:
      case NotificationType.orderCancelled:
      case NotificationType.error:
        return Colors.red;
      case NotificationType.messageNew:
        return Colors.purple;
      case NotificationType.ratingNew:
        return Colors.amber;
      case NotificationType.reminder:
        return Colors.teal;
      case NotificationType.info:
        return Colors.blue;
      case NotificationType.warning:
        return Colors.orange;
    }
  }

  /// تحديد ما إذا كان الإشعار مهم
  bool get isImportant {
    switch (this) {
      case NotificationType.orderNew:
      case NotificationType.orderAccepted:
      case NotificationType.orderRejected:
      case NotificationType.orderCancelled:
      case NotificationType.error:
        return true;
      default:
        return false;
    }
  }
}

/// مساعد لإنشاء إشعارات مختلفة
class NotificationHelper {
  /// إنشاء إشعار طلب جديد للحرفي
  static NotificationModel createNewOrderNotification({
    required String notificationId,
    required String craftsmanId,
    required String orderId,
    required String customerName,
    required String craftType,
  }) {
    return NotificationModel(
      id: notificationId,
      title: 'طلب جديد',
      message: 'لديك طلب جديد من $customerName لخدمة $craftType',
      type: NotificationType.orderNew,
      isRead: false,
      createdAt: DateTime.now(),
      userId: craftsmanId,
      relatedId: orderId,
      data: {
        'customer_name': customerName,
        'craft_type': craftType,
      },
    );
  }

  /// إنشاء إشعار قبول الطلب للعميل
  static NotificationModel createOrderAcceptedNotification({
    required String notificationId,
    required String customerId,
    required String orderId,
    required String craftsmanName,
  }) {
    return NotificationModel(
      id: notificationId,
      title: 'تم قبول طلبك',
      message: 'قبل الحرفي $craftsmanName طلبك وسيتواصل معك قريباً',
      type: NotificationType.orderAccepted,
      isRead: false,
      createdAt: DateTime.now(),
      userId: customerId,
      relatedId: orderId,
      data: {
        'craftsman_name': craftsmanName,
      },
    );
  }

  /// إنشاء إشعار رفض الطلب للعميل
  static NotificationModel createOrderRejectedNotification({
    required String notificationId,
    required String customerId,
    required String orderId,
    required String craftsmanName,
    String? reason,
  }) {
    return NotificationModel(
      id: notificationId,
      title: 'تم رفض طلبك',
      message:
          'رفض الحرفي $craftsmanName طلبك${reason != null ? ': $reason' : ''}',
      type: NotificationType.orderRejected,
      isRead: false,
      createdAt: DateTime.now(),
      userId: customerId,
      relatedId: orderId,
      data: {
        'craftsman_name': craftsmanName,
        'reason': reason,
      },
    );
  }

  /// إنشاء إشعار إكمال الطلب للعميل
  static NotificationModel createOrderCompletedNotification({
    required String notificationId,
    required String customerId,
    required String orderId,
    required String craftsmanName,
  }) {
    return NotificationModel(
      id: notificationId,
      title: 'تم إكمال طلبك',
      message: 'أكمل الحرفي $craftsmanName طلبك. يمكنك الآن تقييم الخدمة',
      type: NotificationType.orderCompleted,
      isRead: false,
      createdAt: DateTime.now(),
      userId: customerId,
      relatedId: orderId,
      data: {
        'craftsman_name': craftsmanName,
      },
    );
  }

  /// إنشاء إشعار رسالة جديدة
  static NotificationModel createNewMessageNotification({
    required String notificationId,
    required String userId,
    required String senderId,
    required String senderName,
    required String messagePreview,
  }) {
    return NotificationModel(
      id: notificationId,
      title: 'رسالة جديدة',
      message: 'رسالة جديدة من $senderName: $messagePreview',
      type: NotificationType.messageNew,
      isRead: false,
      createdAt: DateTime.now(),
      userId: userId,
      relatedId: senderId,
      data: {
        'sender_name': senderName,
        'message_preview': messagePreview,
      },
    );
  }

  /// إنشاء إشعار تقييم جديد للحرفي
  static NotificationModel createNewRatingNotification({
    required String notificationId,
    required String craftsmanId,
    required String orderId,
    required String customerName,
    required double rating,
  }) {
    return NotificationModel(
      id: notificationId,
      title: 'تقييم جديد',
      message: 'قيمك العميل $customerName بـ $rating نجوم',
      type: NotificationType.ratingNew,
      isRead: false,
      createdAt: DateTime.now(),
      userId: craftsmanId,
      relatedId: orderId,
      data: {
        'customer_name': customerName,
        'rating': rating,
      },
    );
  }
}
