import 'package:flutter/foundation.dart';
import '../repositories/notification_repository.dart';
import '../models/notification_model.dart';
import 'auth_service.dart';

/// خدمة الإشعارات - تدير إنشاء وإرسال الإشعارات
class NotificationService {
  final NotificationRepository _notificationRepository = NotificationRepository();
  final AuthService _authService = AuthService();

  /// إنشاء إشعارات تجريبية للاختبار
  Future<void> createSampleNotifications() async {
    try {
      final currentUser = await _authService.getCurrentUser();
      if (currentUser == null) {
        debugPrint('⚠️ لا يوجد مستخدم مسجل لإنشاء الإشعارات');
        return;
      }

      final userId = currentUser.userId.toString();
      final now = DateTime.now();

      // إنشاء إشعارات تجريبية متنوعة
      final sampleNotifications = [
        NotificationModel(
          id: 'notif_1_${now.millisecondsSinceEpoch}',
          title: 'مرحباً بك في الموقف',
          message: 'نرحب بك في تطبيق الموقف لربط العملاء بالحرفيين المهرة',
          type: NotificationType.info,
          isRead: false,
          createdAt: now.subtract(const Duration(minutes: 5)),
          userId: userId,
        ),
        NotificationModel(
          id: 'notif_2_${now.millisecondsSinceEpoch}',
          title: 'طلب جديد',
          message: 'لديك طلب جديد من أحمد محمد لخدمة كهربائي',
          type: NotificationType.orderNew,
          isRead: false,
          createdAt: now.subtract(const Duration(hours: 1)),
          userId: userId,
          relatedId: 'order_123',
        ),
        NotificationModel(
          id: 'notif_3_${now.millisecondsSinceEpoch}',
          title: 'تم قبول طلبك',
          message: 'قبل الحرفي محمد علي طلبك وسيتواصل معك قريباً',
          type: NotificationType.orderAccepted,
          isRead: true,
          createdAt: now.subtract(const Duration(hours: 3)),
          userId: userId,
          relatedId: 'order_124',
        ),
        NotificationModel(
          id: 'notif_4_${now.millisecondsSinceEpoch}',
          title: 'رسالة جديدة',
          message: 'رسالة جديدة من فاطمة الزهراء: متى يمكنك البدء في العمل؟',
          type: NotificationType.messageNew,
          isRead: false,
          createdAt: now.subtract(const Duration(hours: 6)),
          userId: userId,
          relatedId: 'user_456',
        ),
        NotificationModel(
          id: 'notif_5_${now.millisecondsSinceEpoch}',
          title: 'تم إكمال طلبك',
          message: 'أكمل الحرفي يوسف حسن طلبك. يمكنك الآن تقييم الخدمة',
          type: NotificationType.orderCompleted,
          isRead: true,
          createdAt: now.subtract(const Duration(days: 1)),
          userId: userId,
          relatedId: 'order_125',
        ),
        NotificationModel(
          id: 'notif_6_${now.millisecondsSinceEpoch}',
          title: 'تقييم جديد',
          message: 'قيمك العميل سارة أحمد بـ 5 نجوم',
          type: NotificationType.ratingNew,
          isRead: false,
          createdAt: now.subtract(const Duration(days: 2)),
          userId: userId,
          relatedId: 'order_126',
        ),
        NotificationModel(
          id: 'notif_7_${now.millisecondsSinceEpoch}',
          title: 'تذكير',
          message: 'لديك موعد مع العميل خالد محمد غداً في الساعة 10:00 صباحاً',
          type: NotificationType.reminder,
          isRead: false,
          createdAt: now.subtract(const Duration(days: 3)),
          userId: userId,
          relatedId: 'appointment_789',
        ),
        NotificationModel(
          id: 'notif_8_${now.millisecondsSinceEpoch}',
          title: 'تم رفض الطلب',
          message: 'رفض الحرفي علي حسن طلبك: غير متاح في الوقت المحدد',
          type: NotificationType.orderRejected,
          isRead: true,
          createdAt: now.subtract(const Duration(days: 5)),
          userId: userId,
          relatedId: 'order_127',
        ),
      ];

      // حفظ الإشعارات في قاعدة البيانات
      final success = await _notificationRepository.createNotifications(sampleNotifications);
      
      if (success) {
        debugPrint('✅ تم إنشاء ${sampleNotifications.length} إشعار تجريبي');
      } else {
        debugPrint('❌ فشل في إنشاء الإشعارات التجريبية');
      }
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء الإشعارات التجريبية: $e');
    }
  }

  /// إنشاء إشعار طلب جديد للحرفي
  Future<bool> createNewOrderNotification({
    required String craftsmanId,
    required String orderId,
    required String customerName,
    required String craftType,
  }) async {
    try {
      final notification = NotificationHelper.createNewOrderNotification(
        notificationId: 'order_new_${DateTime.now().millisecondsSinceEpoch}',
        craftsmanId: craftsmanId,
        orderId: orderId,
        customerName: customerName,
        craftType: craftType,
      );

      return await _notificationRepository.createNotification(notification);
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء إشعار الطلب الجديد: $e');
      return false;
    }
  }

  /// إنشاء إشعار قبول الطلب للعميل
  Future<bool> createOrderAcceptedNotification({
    required String customerId,
    required String orderId,
    required String craftsmanName,
  }) async {
    try {
      final notification = NotificationHelper.createOrderAcceptedNotification(
        notificationId: 'order_accepted_${DateTime.now().millisecondsSinceEpoch}',
        customerId: customerId,
        orderId: orderId,
        craftsmanName: craftsmanName,
      );

      return await _notificationRepository.createNotification(notification);
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء إشعار قبول الطلب: $e');
      return false;
    }
  }

  /// إنشاء إشعار رفض الطلب للعميل
  Future<bool> createOrderRejectedNotification({
    required String customerId,
    required String orderId,
    required String craftsmanName,
    String? reason,
  }) async {
    try {
      final notification = NotificationHelper.createOrderRejectedNotification(
        notificationId: 'order_rejected_${DateTime.now().millisecondsSinceEpoch}',
        customerId: customerId,
        orderId: orderId,
        craftsmanName: craftsmanName,
        reason: reason,
      );

      return await _notificationRepository.createNotification(notification);
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء إشعار رفض الطلب: $e');
      return false;
    }
  }

  /// إنشاء إشعار إكمال الطلب للعميل
  Future<bool> createOrderCompletedNotification({
    required String customerId,
    required String orderId,
    required String craftsmanName,
  }) async {
    try {
      final notification = NotificationHelper.createOrderCompletedNotification(
        notificationId: 'order_completed_${DateTime.now().millisecondsSinceEpoch}',
        customerId: customerId,
        orderId: orderId,
        craftsmanName: craftsmanName,
      );

      return await _notificationRepository.createNotification(notification);
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء إشعار إكمال الطلب: $e');
      return false;
    }
  }

  /// إنشاء إشعار رسالة جديدة
  Future<bool> createNewMessageNotification({
    required String userId,
    required String senderId,
    required String senderName,
    required String messagePreview,
  }) async {
    try {
      final notification = NotificationHelper.createNewMessageNotification(
        notificationId: 'message_new_${DateTime.now().millisecondsSinceEpoch}',
        userId: userId,
        senderId: senderId,
        senderName: senderName,
        messagePreview: messagePreview,
      );

      return await _notificationRepository.createNotification(notification);
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء إشعار الرسالة الجديدة: $e');
      return false;
    }
  }

  /// إنشاء إشعار تقييم جديد للحرفي
  Future<bool> createNewRatingNotification({
    required String craftsmanId,
    required String orderId,
    required String customerName,
    required double rating,
  }) async {
    try {
      final notification = NotificationHelper.createNewRatingNotification(
        notificationId: 'rating_new_${DateTime.now().millisecondsSinceEpoch}',
        craftsmanId: craftsmanId,
        orderId: orderId,
        customerName: customerName,
        rating: rating,
      );

      return await _notificationRepository.createNotification(notification);
    } catch (e) {
      debugPrint('❌ خطأ في إنشاء إشعار التقييم الجديد: $e');
      return false;
    }
  }

  /// الحصول على عدد الإشعارات غير المقروءة للمستخدم الحالي
  Future<int> getUnreadNotificationsCount() async {
    try {
      final currentUser = await _authService.getCurrentUser();
      if (currentUser == null) return 0;

      return await _notificationRepository.getUnreadNotificationsCount(
        currentUser.userId.toString(),
      );
    } catch (e) {
      debugPrint('❌ خطأ في جلب عدد الإشعارات غير المقروءة: $e');
      return 0;
    }
  }

  /// تنظيف الإشعارات القديمة (يتم استدعاؤها دورياً)
  Future<void> cleanupOldNotifications() async {
    try {
      await _notificationRepository.deleteOldNotifications(30); // حذف الإشعارات الأقدم من 30 يوم
      debugPrint('✅ تم تنظيف الإشعارات القديمة');
    } catch (e) {
      debugPrint('❌ خطأ في تنظيف الإشعارات القديمة: $e');
    }
  }
}
