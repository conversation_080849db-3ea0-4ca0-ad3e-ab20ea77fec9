class UserModel {
  final String id;
  final String name;
  final String email;
  final String phone;
  final String userType; // 'customer' أو 'craftsman'
  final String? profileImage;
  final String? address;
  final double? latitude;
  final double? longitude;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isActive;
  final bool isVerified;
  
  // خاص بالحرفيين
  final String? craftType;
  final String? description;
  final List<String>? skills;
  final double? rating;
  final int? completedOrders;
  final List<String>? workImages;
  final Map<String, dynamic>? workingHours;
  final bool? isAvailable;
  
  const UserModel({
    required this.id,
    required this.name,
    required this.email,
    required this.phone,
    required this.userType,
    this.profileImage,
    this.address,
    this.latitude,
    this.longitude,
    required this.createdAt,
    required this.updatedAt,
    this.isActive = true,
    this.isVerified = false,
    // خاص بالحرفيين
    this.craftType,
    this.description,
    this.skills,
    this.rating,
    this.completedOrders,
    this.workImages,
    this.workingHours,
    this.isAvailable,
  });
  
  // تحويل من JSON
  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      email: json['email'] ?? '',
      phone: json['phone'] ?? '',
      userType: json['user_type'] ?? 'customer',
      profileImage: json['profile_image'],
      address: json['address'],
      latitude: json['latitude']?.toDouble(),
      longitude: json['longitude']?.toDouble(),
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(json['updated_at'] ?? DateTime.now().toIso8601String()),
      isActive: json['is_active'] ?? true,
      isVerified: json['is_verified'] ?? false,
      // خاص بالحرفيين
      craftType: json['craft_type'],
      description: json['description'],
      skills: json['skills'] != null ? List<String>.from(json['skills']) : null,
      rating: json['rating']?.toDouble(),
      completedOrders: json['completed_orders'],
      workImages: json['work_images'] != null ? List<String>.from(json['work_images']) : null,
      workingHours: json['working_hours'],
      isAvailable: json['is_available'],
    );
  }
  
  // تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phone': phone,
      'user_type': userType,
      'profile_image': profileImage,
      'address': address,
      'latitude': latitude,
      'longitude': longitude,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'is_active': isActive,
      'is_verified': isVerified,
      // خاص بالحرفيين
      'craft_type': craftType,
      'description': description,
      'skills': skills,
      'rating': rating,
      'completed_orders': completedOrders,
      'work_images': workImages,
      'working_hours': workingHours,
      'is_available': isAvailable,
    };
  }
  
  // نسخ مع تعديل
  UserModel copyWith({
    String? id,
    String? name,
    String? email,
    String? phone,
    String? userType,
    String? profileImage,
    String? address,
    double? latitude,
    double? longitude,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isActive,
    bool? isVerified,
    // خاص بالحرفيين
    String? craftType,
    String? description,
    List<String>? skills,
    double? rating,
    int? completedOrders,
    List<String>? workImages,
    Map<String, dynamic>? workingHours,
    bool? isAvailable,
  }) {
    return UserModel(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      userType: userType ?? this.userType,
      profileImage: profileImage ?? this.profileImage,
      address: address ?? this.address,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isActive: isActive ?? this.isActive,
      isVerified: isVerified ?? this.isVerified,
      // خاص بالحرفيين
      craftType: craftType ?? this.craftType,
      description: description ?? this.description,
      skills: skills ?? this.skills,
      rating: rating ?? this.rating,
      completedOrders: completedOrders ?? this.completedOrders,
      workImages: workImages ?? this.workImages,
      workingHours: workingHours ?? this.workingHours,
      isAvailable: isAvailable ?? this.isAvailable,
    );
  }
  
  // التحقق من كون المستخدم حرفي
  bool get isCraftsman => userType == 'craftsman';
  
  // التحقق من كون المستخدم عميل
  bool get isCustomer => userType == 'customer';
  
  // الحصول على النجوم كنص
  String get ratingText {
    if (rating == null) return 'غير مقيم';
    return '${rating!.toStringAsFixed(1)} ⭐';
  }
  
  // الحصول على عدد الطلبات المكتملة كنص
  String get completedOrdersText {
    if (completedOrders == null) return '0 طلب';
    return '$completedOrders طلب مكتمل';
  }
  
  // التحقق من اكتمال البيانات الأساسية
  bool get isProfileComplete {
    bool basicComplete = name.isNotEmpty && 
                        email.isNotEmpty && 
                        phone.isNotEmpty &&
                        address != null &&
                        address!.isNotEmpty;
    
    if (isCraftsman) {
      return basicComplete && 
             craftType != null && 
             craftType!.isNotEmpty &&
             description != null &&
             description!.isNotEmpty;
    }
    
    return basicComplete;
  }
  
  @override
  String toString() {
    return 'UserModel(id: $id, name: $name, email: $email, userType: $userType)';
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is UserModel && other.id == id;
  }
  
  @override
  int get hashCode => id.hashCode;
}
