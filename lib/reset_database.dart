import 'package:flutter/material.dart';
import 'core/services/database_service.dart';

/// أداة لإعادة تعيين قاعدة البيانات (للتطوير فقط)
class DatabaseResetHelper {
  static Future<void> resetDatabase() async {
    try {
      debugPrint('🔄 بدء إعادة تعيين قاعدة البيانات...');

      // استخدام الدالة الجديدة لإعادة التعيين الفوري
      final dbService = DatabaseService();
      await dbService.forceReset();

      debugPrint('✅ تم إنشاء قاعدة البيانات الجديدة مع البيانات المحدثة');
    } catch (e) {
      debugPrint('❌ خطأ في إعادة تعيين قاعدة البيانات: $e');
      rethrow;
    }
  }
}

/// شاشة إعادة تعيين قاعدة البيانات (للتطوير فقط)
class DatabaseResetScreen extends StatefulWidget {
  const DatabaseResetScreen({super.key});

  @override
  State<DatabaseResetScreen> createState() => _DatabaseResetScreenState();
}

class _DatabaseResetScreenState extends State<DatabaseResetScreen> {
  bool _isResetting = false;
  String _status = 'جاهز لإعادة التعيين';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إعادة تعيين قاعدة البيانات'),
        backgroundColor: Colors.red,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.warning,
              size: 64,
              color: Colors.orange,
            ),
            const SizedBox(height: 16),
            const Text(
              'تحذير!',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: Colors.red,
              ),
            ),
            const SizedBox(height: 16),
            const Text(
              'هذه العملية ستحذف جميع البيانات الموجودة في قاعدة البيانات وتعيد إنشاءها من جديد.',
              textAlign: TextAlign.center,
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 8),
            const Text(
              'سيتم فقدان جميع المستخدمين والطلبات المسجلة.',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                color: Colors.red,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 32),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey[100],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey[300]!),
              ),
              child: Column(
                children: [
                  const Text(
                    'الحالة:',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _status,
                    style: TextStyle(
                      fontSize: 14,
                      color: _isResetting ? Colors.orange : Colors.green,
                    ),
                  ),
                  if (_isResetting) ...[
                    const SizedBox(height: 16),
                    const CircularProgressIndicator(),
                  ],
                ],
              ),
            ),
            const SizedBox(height: 32),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isResetting ? null : _resetDatabase,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.red,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: Text(
                  _isResetting
                      ? 'جاري إعادة التعيين...'
                      : 'إعادة تعيين قاعدة البيانات',
                  style: const TextStyle(fontSize: 16),
                ),
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: OutlinedButton(
                onPressed:
                    _isResetting ? null : () => Navigator.of(context).pop(),
                child: const Text('إلغاء'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _resetDatabase() async {
    setState(() {
      _isResetting = true;
      _status = 'جاري حذف قاعدة البيانات القديمة...';
    });

    try {
      await DatabaseResetHelper.resetDatabase();

      setState(() {
        _status = 'تم إعادة تعيين قاعدة البيانات بنجاح!';
        _isResetting = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إعادة تعيين قاعدة البيانات بنجاح!'),
            backgroundColor: Colors.green,
          ),
        );

        // العودة للشاشة السابقة بعد ثانيتين
        Future.delayed(const Duration(seconds: 2), () {
          if (mounted) {
            Navigator.of(context).pop();
          }
        });
      }
    } catch (e) {
      setState(() {
        _status = 'خطأ في إعادة التعيين: $e';
        _isResetting = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إعادة التعيين: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
