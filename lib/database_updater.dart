import 'package:flutter/material.dart';
import 'core/services/database_service.dart';
import 'cities_data_checker.dart';

/// أداة تحديث قاعدة البيانات مع المدن الشاملة
class DatabaseUpdater {
  
  /// تحديث قاعدة البيانات مع المدن الشاملة
  static Future<Map<String, dynamic>> updateDatabaseWithCompleteCities() async {
    try {
      debugPrint('🔄 بدء تحديث قاعدة البيانات مع المدن الشاملة...');
      
      final dbService = DatabaseService();
      
      // حذف قاعدة البيانات القديمة
      debugPrint('🗑️ حذف قاعدة البيانات القديمة...');
      await dbService.databaseHelper.deleteDatabase();
      
      // إعادة تهيئة قاعدة البيانات الجديدة
      debugPrint('🏗️ إنشاء قاعدة البيانات الجديدة...');
      await dbService.initialize();
      
      // فحص النتائج
      debugPrint('🔍 فحص النتائج...');
      final checkResult = await CitiesDataChecker.checkCitiesData();
      
      if (checkResult['status'] == 'success') {
        final summary = checkResult['summary'];
        debugPrint('✅ تم تحديث قاعدة البيانات بنجاح!');
        debugPrint('   - إجمالي المدن: ${summary['total_cities']}');
        debugPrint('   - إجمالي الجهات: ${summary['total_regions']}');
        debugPrint('   - نسبة الإكمال: ${summary['completion_percentage']}%');
        
        return {
          'status': 'success',
          'message': 'تم تحديث قاعدة البيانات بنجاح',
          'cities_count': summary['total_cities'],
          'regions_count': summary['total_regions'],
          'completion_percentage': summary['completion_percentage'],
          'timestamp': DateTime.now().toIso8601String(),
        };
      } else {
        return {
          'status': 'error',
          'message': 'فشل في فحص قاعدة البيانات بعد التحديث',
          'error': checkResult['error'],
          'timestamp': DateTime.now().toIso8601String(),
        };
      }
      
    } catch (e) {
      debugPrint('❌ خطأ في تحديث قاعدة البيانات: $e');
      return {
        'status': 'error',
        'message': 'حدث خطأ أثناء تحديث قاعدة البيانات',
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }
  
  /// طباعة تقرير التحديث
  static Future<void> printUpdateReport() async {
    debugPrint('📊 تقرير تحديث قاعدة البيانات');
    debugPrint('=' * 50);
    
    final result = await updateDatabaseWithCompleteCities();
    
    if (result['status'] == 'success') {
      debugPrint('✅ نجح التحديث!');
      debugPrint('   📍 عدد المدن: ${result['cities_count']}');
      debugPrint('   🗺️ عدد الجهات: ${result['regions_count']}');
      debugPrint('   📈 نسبة الإكمال: ${result['completion_percentage']}%');
      debugPrint('   ⏰ وقت التحديث: ${result['timestamp']}');
    } else {
      debugPrint('❌ فشل التحديث!');
      debugPrint('   📝 الرسالة: ${result['message']}');
      debugPrint('   🐛 الخطأ: ${result['error']}');
      debugPrint('   ⏰ وقت المحاولة: ${result['timestamp']}');
    }
    
    debugPrint('=' * 50);
  }
}

/// شاشة تحديث قاعدة البيانات
class DatabaseUpdateScreen extends StatefulWidget {
  const DatabaseUpdateScreen({super.key});

  @override
  State<DatabaseUpdateScreen> createState() => _DatabaseUpdateScreenState();
}

class _DatabaseUpdateScreenState extends State<DatabaseUpdateScreen> {
  Map<String, dynamic>? _updateResult;
  bool _isUpdating = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تحديث قاعدة البيانات'),
        backgroundColor: Colors.purple,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'تحديث قاعدة البيانات مع المدن الشاملة',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'سيتم تحديث قاعدة البيانات لتشمل:',
                      style: TextStyle(fontSize: 16),
                    ),
                    const SizedBox(height: 8),
                    const Text('• 266 مدينة مغربية شاملة'),
                    const Text('• 12 جهة مغربية'),
                    const Text('• المعرفات المطلوبة (80، 178، 200، 266)'),
                    const Text('• أسماء باللغات الثلاث (عربي، فرنسي، إنجليزي)'),
                    const Text('• توزيع عادل على جميع الجهات'),
                    const SizedBox(height: 16),
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.orange.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.orange.withOpacity(0.3)),
                      ),
                      child: const Row(
                        children: [
                          Icon(Icons.warning, color: Colors.orange),
                          SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              'تحذير: سيتم حذف قاعدة البيانات الحالية وإعادة إنشائها',
                              style: TextStyle(color: Colors.orange),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            ElevatedButton.icon(
              onPressed: _isUpdating ? null : _updateDatabase,
              icon: _isUpdating 
                ? const SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Icon(Icons.update),
              label: Text(_isUpdating ? 'جاري التحديث...' : 'تحديث قاعدة البيانات'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.purple,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
            ),
            
            const SizedBox(height: 16),
            
            if (_updateResult != null) _buildResultCard(),
          ],
        ),
      ),
    );
  }

  Future<void> _updateDatabase() async {
    setState(() {
      _isUpdating = true;
      _updateResult = null;
    });

    try {
      // تشغيل التحديث مع طباعة التقرير
      await DatabaseUpdater.printUpdateReport();
      
      // الحصول على النتيجة
      final result = await DatabaseUpdater.updateDatabaseWithCompleteCities();
      
      setState(() {
        _updateResult = result;
      });
    } catch (e) {
      setState(() {
        _updateResult = {
          'status': 'error',
          'message': 'حدث خطأ غير متوقع',
          'error': e.toString(),
        };
      });
    } finally {
      setState(() {
        _isUpdating = false;
      });
    }
  }

  Widget _buildResultCard() {
    final isSuccess = _updateResult!['status'] == 'success';
    final color = isSuccess ? Colors.green : Colors.red;
    
    return Card(
      color: color.withOpacity(0.1),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  isSuccess ? Icons.check_circle : Icons.error,
                  color: color,
                ),
                const SizedBox(width: 8),
                Text(
                  isSuccess ? 'نجح التحديث!' : 'فشل التحديث!',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            Text(
              'الرسالة: ${_updateResult!['message']}',
              style: const TextStyle(fontSize: 14),
            ),
            
            if (isSuccess) ...[
              const SizedBox(height: 8),
              Text('عدد المدن: ${_updateResult!['cities_count']}'),
              Text('عدد الجهات: ${_updateResult!['regions_count']}'),
              Text('نسبة الإكمال: ${_updateResult!['completion_percentage']}%'),
            ],
            
            if (!isSuccess) ...[
              const SizedBox(height: 8),
              Text(
                'تفاصيل الخطأ: ${_updateResult!['error']}',
                style: const TextStyle(fontSize: 12, color: Colors.grey),
              ),
            ],
            
            const SizedBox(height: 16),
            
            Row(
              children: [
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => const CitiesDataCheckerScreen(),
                        ),
                      );
                    },
                    child: const Text('فحص النتائج'),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _updateDatabase,
                    child: const Text('إعادة المحاولة'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
