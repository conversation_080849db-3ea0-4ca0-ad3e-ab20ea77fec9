import 'package:flutter/material.dart';
import '../../shared/themes/app_theme.dart';
import '../../core/repositories/order_repository.dart';
import '../../core/services/auth_service.dart';
import '../../core/services/notification_service.dart';
import '../../core/models/order_model.dart';

/// صفحة الطلبات للحرفي
class CraftsmanOrdersScreen extends StatefulWidget {
  const CraftsmanOrdersScreen({super.key});

  @override
  State<CraftsmanOrdersScreen> createState() => _CraftsmanOrdersScreenState();
}

class _CraftsmanOrdersScreenState extends State<CraftsmanOrdersScreen>
    with SingleTickerProviderStateMixin {
  final OrderRepository _orderRepository = OrderRepository();
  final AuthService _authService = AuthService();
  final NotificationService _notificationService = NotificationService();

  late TabController _tabController;

  List<OrderModel> _allOrders = [];
  List<OrderModel> _newOrders = [];
  List<OrderModel> _acceptedOrders = [];
  List<OrderModel> _completedOrders = [];
  List<OrderModel> _rejectedOrders = [];

  bool _isLoading = true;
  int _unreadCount = 0;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadOrders();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// تحميل الطلبات
  Future<void> _loadOrders() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final currentUser = await _authService.getCurrentUser();
      if (currentUser == null) {
        debugPrint('⚠️ لا يوجد مستخدم مسجل');
        setState(() {
          _isLoading = false;
        });
        return;
      }

      // جلب جميع طلبات الحرفي
      _allOrders =
          await _orderRepository.getCraftsmanOrders(currentUser.userId);

      // تصنيف الطلبات حسب الحالة
      _categorizeOrders();

      // حساب عدد الطلبات الجديدة غير المقروءة
      _unreadCount = _newOrders.length;

      setState(() {
        _isLoading = false;
      });

      debugPrint('✅ تم تحميل ${_allOrders.length} طلب');
    } catch (e) {
      debugPrint('❌ خطأ في تحميل الطلبات: $e');
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل الطلبات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// تصنيف الطلبات حسب الحالة
  void _categorizeOrders() {
    _newOrders = _allOrders
        .where((order) => order.status == OrderStatus.pending)
        .toList();
    _acceptedOrders = _allOrders
        .where((order) => order.status == OrderStatus.accepted)
        .toList();
    _completedOrders = _allOrders
        .where((order) => order.status == OrderStatus.completed)
        .toList();
    _rejectedOrders = _allOrders
        .where((order) => order.status == OrderStatus.rejected)
        .toList();

    // ترتيب حسب التاريخ (الأحدث أولاً)
    _newOrders.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    _acceptedOrders.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    _completedOrders.sort((a, b) => b.createdAt.compareTo(a.createdAt));
    _rejectedOrders.sort((a, b) => b.createdAt.compareTo(a.createdAt));
  }

  /// قبول طلب
  Future<void> _acceptOrder(OrderModel order) async {
    try {
      final success = await _orderRepository.updateOrderStatus(
        order.id!,
        OrderStatus.accepted,
      );

      if (success) {
        // إرسال إشعار للعميل
        await _notificationService.createOrderAcceptedNotification(
          customerId: order.customerId.toString(),
          orderId: order.id!.toString(),
          craftsmanName: 'الحرفي', // يمكن تحسينه لاحقاً
        );

        // إعادة تحميل الطلبات
        await _loadOrders();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم قبول الطلب بنجاح'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('❌ خطأ في قبول الطلب: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في قبول الطلب: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// رفض طلب
  Future<void> _rejectOrder(OrderModel order) async {
    try {
      final success = await _orderRepository.updateOrderStatus(
        order.id!,
        OrderStatus.rejected,
      );

      if (success) {
        // إرسال إشعار للعميل
        await _notificationService.createOrderRejectedNotification(
          customerId: order.customerId.toString(),
          orderId: order.id!.toString(),
          craftsmanName: 'الحرفي', // يمكن تحسينه لاحقاً
        );

        // إعادة تحميل الطلبات
        await _loadOrders();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم رفض الطلب'),
              backgroundColor: Colors.orange,
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('❌ خطأ في رفض الطلب: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في رفض الطلب: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// تحديد طلب كمكتمل
  Future<void> _completeOrder(OrderModel order) async {
    try {
      final success = await _orderRepository.updateOrderStatus(
        order.id!,
        OrderStatus.completed,
      );

      if (success) {
        // إرسال إشعار للعميل
        await _notificationService.createOrderCompletedNotification(
          customerId: order.customerId.toString(),
          orderId: order.id!.toString(),
          craftsmanName: 'الحرفي', // يمكن تحسينه لاحقاً
        );

        // إعادة تحميل الطلبات
        await _loadOrders();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('تم تحديد الطلب كمكتمل'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e) {
      debugPrint('❌ خطأ في إكمال الطلب: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إكمال الطلب: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          'الطلبات',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppTheme.primaryColor,
        elevation: 0,
        actions: [
          // عداد الطلبات الجديدة
          if (_unreadCount > 0)
            Container(
              margin: const EdgeInsets.only(right: 16, top: 8),
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.red,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                '$_unreadCount',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: [
            Tab(
              text:
                  'جديدة${_newOrders.isNotEmpty ? ' (${_newOrders.length})' : ''}',
            ),
            Tab(
              text:
                  'مقبولة${_acceptedOrders.isNotEmpty ? ' (${_acceptedOrders.length})' : ''}',
            ),
            Tab(
              text:
                  'مكتملة${_completedOrders.isNotEmpty ? ' (${_completedOrders.length})' : ''}',
            ),
            Tab(
              text:
                  'مرفوضة${_rejectedOrders.isNotEmpty ? ' (${_rejectedOrders.length})' : ''}',
            ),
          ],
        ),
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadOrders,
      child: TabBarView(
        controller: _tabController,
        children: [
          _buildOrdersList(_newOrders, showActions: true),
          _buildOrdersList(_acceptedOrders, showCompleteAction: true),
          _buildOrdersList(_completedOrders),
          _buildOrdersList(_rejectedOrders),
        ],
      ),
    );
  }

  Widget _buildOrdersList(
    List<OrderModel> orders, {
    bool showActions = false,
    bool showCompleteAction = false,
  }) {
    if (orders.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: orders.length,
      itemBuilder: (context, index) {
        final order = orders[index];
        return _buildOrderCard(
          order,
          showActions: showActions,
          showCompleteAction: showCompleteAction,
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.assignment_outlined,
            size: 80,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد طلبات',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.grey[600],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'ستظهر طلباتك هنا عند وصولها',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[500],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildOrderCard(
    OrderModel order, {
    bool showActions = false,
    bool showCompleteAction = false,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 6,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // رأس البطاقة
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'طلب #${order.id}',
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'العميل: غير محدد',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),
                _buildStatusChip(order.status),
              ],
            ),
            const SizedBox(height: 12),

            // تفاصيل الطلب
            _buildOrderDetails(order),

            // الأزرار
            if (showActions || showCompleteAction) ...[
              const SizedBox(height: 16),
              _buildActionButtons(
                order,
                showActions: showActions,
                showCompleteAction: showCompleteAction,
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChip(OrderStatus status) {
    Color color;
    String text;

    switch (status) {
      case OrderStatus.pending:
        color = Colors.orange;
        text = 'جديد';
        break;
      case OrderStatus.accepted:
        color = Colors.blue;
        text = 'مقبول';
        break;
      case OrderStatus.completed:
        color = Colors.green;
        text = 'مكتمل';
        break;
      case OrderStatus.rejected:
        color = Colors.red;
        text = 'مرفوض';
        break;
      case OrderStatus.cancelled:
        color = Colors.grey;
        text = 'ملغي';
        break;
      case OrderStatus.inProgress:
        color = Colors.blue;
        text = 'قيد التنفيذ';
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  Widget _buildOrderDetails(OrderModel order) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildDetailRow(
          icon: Icons.build,
          label: 'نوع الخدمة',
          value: 'غير محدد',
        ),
        const SizedBox(height: 8),
        _buildDetailRow(
          icon: Icons.description,
          label: 'الوصف',
          value:
              order.description.isNotEmpty ? order.description : 'لا يوجد وصف',
        ),
        const SizedBox(height: 8),
        _buildDetailRow(
          icon: Icons.access_time,
          label: 'تاريخ الطلب',
          value: _formatDateTime(order.createdAt),
        ),
        if (order.scheduledDate != null) ...[
          const SizedBox(height: 8),
          _buildDetailRow(
            icon: Icons.event,
            label: 'الموعد المحدد',
            value: _formatDateTime(order.scheduledDate!),
          ),
        ],
      ],
    );
  }

  Widget _buildDetailRow({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          icon,
          size: 16,
          color: Colors.grey[600],
        ),
        const SizedBox(width: 8),
        Expanded(
          child: RichText(
            text: TextSpan(
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[800],
              ),
              children: [
                TextSpan(
                  text: '$label: ',
                  style: const TextStyle(fontWeight: FontWeight.w600),
                ),
                TextSpan(text: value),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons(
    OrderModel order, {
    bool showActions = false,
    bool showCompleteAction = false,
  }) {
    if (showActions) {
      // أزرار قبول ورفض للطلبات الجديدة
      return Row(
        children: [
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () => _acceptOrder(order),
              icon: const Icon(Icons.check, size: 18),
              label: const Text('قبول'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: ElevatedButton.icon(
              onPressed: () => _rejectOrder(order),
              icon: const Icon(Icons.close, size: 18),
              label: const Text('رفض'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
          ),
        ],
      );
    } else if (showCompleteAction) {
      // زر إكمال للطلبات المقبولة
      return SizedBox(
        width: double.infinity,
        child: ElevatedButton.icon(
          onPressed: () => _completeOrder(order),
          icon: const Icon(Icons.task_alt, size: 18),
          label: const Text('تحديد كمكتمل'),
          style: ElevatedButton.styleFrom(
            backgroundColor: AppTheme.primaryColor,
            foregroundColor: Colors.white,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
        ),
      );
    }

    return const SizedBox.shrink();
  }

  /// تنسيق التاريخ والوقت
  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} يوم';
    } else {
      return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
    }
  }
}
