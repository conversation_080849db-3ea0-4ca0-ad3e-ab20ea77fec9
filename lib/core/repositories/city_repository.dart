import '../database/database_helper.dart';
import '../models/city_model.dart';
import '../models/region_model.dart';
import '../utils/logger.dart';

/// مستودع المدن والجهات المحدث
class CityRepository {
  final DatabaseHelper _dbHelper = DatabaseHelper();

  /// الحصول على جميع الجهات مع المدن
  Future<List<RegionModel>> getAllRegions() async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'regions',
        orderBy: 'name_ar ASC',
      );

      List<RegionModel> regions = [];
      for (var map in maps) {
        // الحصول على المدن لكل جهة
        final cities = await getCitiesByRegion(map['id']);
        final region = RegionModel.fromMap(map);
        regions.add(region.copyWith(cities: cities));
      }

      return regions;
    } catch (e) {
      AppLogger.error('خطأ في الحصول على الجهات', e);
      return [];
    }
  }

  /// الحصول على جميع المدن
  Future<List<CityModel>> getAllCities() async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'cities',
        orderBy: 'name_ar ASC',
      );

      return maps.map((map) => CityModel.fromMap(map)).toList();
    } catch (e) {
      AppLogger.error('خطأ في الحصول على المدن', e);
      return [];
    }
  }

  /// الحصول على المدن حسب الجهة
  Future<List<CityModel>> getCitiesByRegion(String regionId) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'cities',
        where: 'region_id = ?',
        whereArgs: [regionId],
        orderBy: 'name_ar ASC',
      );

      return maps.map((map) => CityModel.fromMap(map)).toList();
    } catch (e) {
      AppLogger.info('خطأ في الحصول على مدن الجهة: $e');
      return [];
    }
  }

  /// البحث في المدن
  Future<List<CityModel>> searchCities(String query) async {
    try {
      final db = await _dbHelper.database;
      final searchQuery = '%$query%';

      final List<Map<String, dynamic>> maps = await db.query(
        'cities',
        where: 'name_ar LIKE ? OR name_en LIKE ? OR name_fr LIKE ?',
        whereArgs: [searchQuery, searchQuery, searchQuery],
        orderBy: 'name_ar ASC',
      );

      return maps.map((map) => CityModel.fromMap(map)).toList();
    } catch (e) {
      AppLogger.info('خطأ في البحث في المدن: $e');
      return [];
    }
  }

  /// البحث في الجهات
  Future<List<RegionModel>> searchRegions(String query) async {
    try {
      final db = await _dbHelper.database;
      final searchQuery = '%$query%';

      final List<Map<String, dynamic>> maps = await db.query(
        'regions',
        where: 'name_ar LIKE ? OR name_en LIKE ? OR name_fr LIKE ?',
        whereArgs: [searchQuery, searchQuery, searchQuery],
        orderBy: 'name_ar ASC',
      );

      List<RegionModel> regions = [];
      for (var map in maps) {
        final cities = await getCitiesByRegion(map['id']);
        final region = RegionModel.fromMap(map);
        regions.add(region.copyWith(cities: cities));
      }

      return regions;
    } catch (e) {
      AppLogger.info('خطأ في البحث في الجهات: $e');
      return [];
    }
  }

  /// الحصول على جهة بالمعرف
  Future<RegionModel?> getRegionById(String regionId) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'regions',
        where: 'id = ?',
        whereArgs: [regionId],
        limit: 1,
      );

      if (maps.isEmpty) return null;

      final cities = await getCitiesByRegion(regionId);
      final region = RegionModel.fromMap(maps.first);
      return region.copyWith(cities: cities);
    } catch (e) {
      AppLogger.info('خطأ في الحصول على الجهة: $e');
      return null;
    }
  }

  /// الحصول على مدينة بالمعرف
  Future<CityModel?> getCityById(String cityId) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'cities',
        where: 'id = ?',
        whereArgs: [cityId],
        limit: 1,
      );

      if (maps.isEmpty) return null;
      return CityModel.fromMap(maps.first);
    } catch (e) {
      AppLogger.info('خطأ في الحصول على المدينة: $e');
      return null;
    }
  }

  /// إضافة مدينة جديدة
  Future<int?> insertCity(CityModel city) async {
    try {
      final db = await _dbHelper.database;
      return await db.insert('cities', city.toMap());
    } catch (e) {
      AppLogger.info('خطأ في إضافة المدينة: $e');
      return null;
    }
  }

  /// تحديث مدينة
  Future<bool> updateCity(CityModel city) async {
    try {
      final db = await _dbHelper.database;
      final result = await db.update(
        'cities',
        city.toMap(),
        where: 'id = ?',
        whereArgs: [city.id],
      );
      return result > 0;
    } catch (e) {
      AppLogger.info('خطأ في تحديث المدينة: $e');
      return false;
    }
  }

  /// حذف مدينة
  Future<bool> deleteCity(String cityId) async {
    try {
      final db = await _dbHelper.database;
      final result = await db.delete(
        'cities',
        where: 'id = ?',
        whereArgs: [cityId],
      );
      return result > 0;
    } catch (e) {
      AppLogger.info('خطأ في حذف المدينة: $e');
      return false;
    }
  }

  /// الحصول على إحصائيات المدن
  Future<Map<String, dynamic>> getCitiesStatistics() async {
    try {
      final db = await _dbHelper.database;

      // عدد الجهات
      final regionsCount =
          await db.rawQuery('SELECT COUNT(*) as count FROM regions');

      // عدد المدن
      final citiesCount =
          await db.rawQuery('SELECT COUNT(*) as count FROM cities');

      // عدد المدن لكل جهة
      final citiesPerRegion = await db.rawQuery('''
        SELECT r.name_ar as region_name, COUNT(c.id) as cities_count
        FROM regions r
        LEFT JOIN cities c ON r.id = c.region_id
        GROUP BY r.id, r.name_ar
        ORDER BY cities_count DESC
      ''');

      return {
        'total_regions': regionsCount.first['count'],
        'total_cities': citiesCount.first['count'],
        'cities_per_region': citiesPerRegion,
      };
    } catch (e) {
      AppLogger.info('خطأ في الحصول على إحصائيات المدن: $e');
      return {};
    }
  }

  /// البحث المتقدم في المدن مع الفلترة
  Future<List<CityModel>> advancedSearchCities({
    String? query,
    String? regionId,
  }) async {
    try {
      final db = await _dbHelper.database;

      String whereClause = '1 = 1';
      List<dynamic> whereArgs = [];

      if (query != null && query.isNotEmpty) {
        whereClause +=
            ' AND (name_ar LIKE ? OR name_en LIKE ? OR name_fr LIKE ?)';
        final searchQuery = '%$query%';
        whereArgs.addAll([searchQuery, searchQuery, searchQuery]);
      }

      if (regionId != null) {
        whereClause += ' AND region_id = ?';
        whereArgs.add(regionId);
      }

      final List<Map<String, dynamic>> maps = await db.query(
        'cities',
        where: whereClause,
        whereArgs: whereArgs,
        orderBy: 'name_ar ASC',
      );

      return maps.map((map) => CityModel.fromMap(map)).toList();
    } catch (e) {
      AppLogger.info('خطأ في البحث المتقدم: $e');
      return [];
    }
  }
}
