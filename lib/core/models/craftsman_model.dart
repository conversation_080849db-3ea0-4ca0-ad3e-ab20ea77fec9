/// نموذج الحرفي
class CraftsmanModel {
  final int? id;
  final String name;
  final String email;
  final String phone;
  final String craftId;
  final String regionId;
  final String cityId;
  final double rating;
  final int completedOrders;
  final bool isActive;
  final DateTime createdAt;
  final DateTime? updatedAt;

  CraftsmanModel({
    this.id,
    required this.name,
    required this.email,
    required this.phone,
    required this.craftId,
    required this.regionId,
    required this.cityId,
    this.rating = 0.0,
    this.completedOrders = 0,
    this.isActive = true,
    required this.createdAt,
    this.updatedAt,
  });

  /// إنشاء نموذج من Map
  factory CraftsmanModel.fromMap(Map<String, dynamic> map) {
    return CraftsmanModel(
      id: map['id']?.toInt(),
      name: map['name'] ?? '',
      email: map['email'] ?? '',
      phone: map['phone'] ?? '',
      craftId: map['craft_id'] ?? '',
      regionId: map['region_id'] ?? '',
      cityId: map['city_id'] ?? '',
      rating: (map['rating'] ?? 0.0).toDouble(),
      completedOrders: map['completed_orders']?.toInt() ?? 0,
      isActive: (map['is_active'] ?? 1) == 1,
      createdAt: DateTime.parse(map['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
    );
  }

  /// تحويل النموذج إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phone': phone,
      'craft_id': craftId,
      'region_id': regionId,
      'city_id': cityId,
      'rating': rating,
      'completed_orders': completedOrders,
      'is_active': isActive ? 1 : 0,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  /// إنشاء نسخة محدثة من النموذج
  CraftsmanModel copyWith({
    int? id,
    String? name,
    String? email,
    String? phone,
    String? craftId,
    String? regionId,
    String? cityId,
    double? rating,
    int? completedOrders,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CraftsmanModel(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phone: phone ?? this.phone,
      craftId: craftId ?? this.craftId,
      regionId: regionId ?? this.regionId,
      cityId: cityId ?? this.cityId,
      rating: rating ?? this.rating,
      completedOrders: completedOrders ?? this.completedOrders,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  String toString() {
    return 'CraftsmanModel(id: $id, name: $name, email: $email, craftId: $craftId, rating: $rating, completedOrders: $completedOrders)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CraftsmanModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  /// التحقق من صحة البيانات
  bool get isValid {
    return name.isNotEmpty &&
        email.isNotEmpty &&
        phone.isNotEmpty &&
        craftId.isNotEmpty &&
        regionId.isNotEmpty &&
        cityId.isNotEmpty &&
        _isValidEmail(email) &&
        _isValidPhone(phone);
  }

  /// التحقق من صحة البريد الإلكتروني
  bool _isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  /// التحقق من صحة رقم الهاتف
  bool _isValidPhone(String phone) {
    return RegExp(r'^\+?[0-9]{10,15}$').hasMatch(phone.replaceAll(' ', ''));
  }

  /// الحصول على التقييم كنص
  String get ratingText {
    if (rating == 0.0) return 'غير مقيم';
    return '${rating.toStringAsFixed(1)} ⭐';
  }

  /// الحصول على عدد الطلبات المكتملة كنص
  String get completedOrdersText {
    if (completedOrders == 0) return 'لا توجد طلبات';
    return '$completedOrders طلب مكتمل';
  }

  /// الحصول على حالة النشاط كنص
  String get statusText {
    return isActive ? 'نشط' : 'غير نشط';
  }

  /// الحصول على اسم مختصر
  String get shortName {
    final parts = name.split(' ');
    if (parts.length >= 2) {
      return '${parts[0]} ${parts[1]}';
    }
    return name;
  }

  /// الحصول على الأحرف الأولى من الاسم
  String get initials {
    final parts = name.split(' ');
    if (parts.length >= 2) {
      return '${parts[0][0]}${parts[1][0]}'.toUpperCase();
    } else if (parts.isNotEmpty) {
      return parts[0][0].toUpperCase();
    }
    return 'ح'; // حرفي
  }

  /// التحقق من كون الحرفي مميز (تقييم عالي)
  bool get isFeatured {
    return rating >= 4.0 && completedOrders >= 10;
  }

  /// الحصول على مستوى الخبرة
  String get experienceLevel {
    if (completedOrders >= 100) return 'خبير';
    if (completedOrders >= 50) return 'متقدم';
    if (completedOrders >= 20) return 'متوسط';
    if (completedOrders >= 5) return 'مبتدئ';
    return 'جديد';
  }
}
