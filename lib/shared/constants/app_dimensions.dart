/// ثوابت الأحجام والمسافات للتطبيق لضمان التناسق
class AppDimensions {
  // المسافات الأساسية
  static const double paddingXS = 4.0;
  static const double paddingS = 8.0;
  static const double paddingM = 16.0;
  static const double paddingL = 24.0;
  static const double paddingXL = 32.0;
  static const double paddingXXL = 48.0;

  // الهوامش
  static const double marginXS = 4.0;
  static const double marginS = 8.0;
  static const double marginM = 16.0;
  static const double marginL = 24.0;
  static const double marginXL = 32.0;
  static const double marginXXL = 48.0;

  // أحجام الأيقونات
  static const double iconXS = 16.0;
  static const double iconS = 20.0;
  static const double iconM = 24.0;
  static const double iconL = 32.0;
  static const double iconXL = 48.0;
  static const double iconXXL = 64.0;

  // أحجام الأزرار
  static const double buttonHeightS = 40.0;
  static const double buttonHeightM = 48.0;
  static const double buttonHeightL = 56.0;
  static const double buttonHeightXL = 64.0;

  // نصف أقطار الحدود
  static const double radiusXS = 4.0;
  static const double radiusS = 8.0;
  static const double radiusM = 12.0;
  static const double radiusL = 16.0;
  static const double radiusXL = 20.0;
  static const double radiusXXL = 24.0;
  static const double radiusCircle = 50.0;

  // أحجام الخطوط
  static const double fontXS = 10.0;
  static const double fontS = 12.0;
  static const double fontM = 14.0;
  static const double fontL = 16.0;
  static const double fontXL = 18.0;
  static const double fontXXL = 20.0;
  static const double fontTitle = 24.0;
  static const double fontHeading = 28.0;
  static const double fontDisplay = 32.0;

  // أحجام الارتفاعات (Elevation)
  static const double elevationXS = 1.0;
  static const double elevationS = 2.0;
  static const double elevationM = 4.0;
  static const double elevationL = 8.0;
  static const double elevationXL = 12.0;
  static const double elevationXXL = 16.0;

  // أحجام الخطوط العريضة
  static const double strokeThin = 1.0;
  static const double strokeMedium = 2.0;
  static const double strokeThick = 3.0;

  // أحجام الصور والأفاتار
  static const double avatarS = 32.0;
  static const double avatarM = 48.0;
  static const double avatarL = 64.0;
  static const double avatarXL = 96.0;
  static const double avatarXXL = 128.0;

  // أحجام البطاقات
  static const double cardMinHeight = 120.0;
  static const double cardMaxWidth = 400.0;

  // أحجام الشاشات
  static const double maxContentWidth = 600.0;
  static const double minTouchTarget = 44.0;

  // مسافات خاصة للشاشات
  static const double screenPadding = paddingL;
  static const double sectionSpacing = paddingXL;
  static const double itemSpacing = paddingM;

  // أحجام الرسوم المتحركة
  static const Duration animationFast = Duration(milliseconds: 150);
  static const Duration animationMedium = Duration(milliseconds: 300);
  static const Duration animationSlow = Duration(milliseconds: 500);
  static const Duration animationVerySlow = Duration(milliseconds: 1000);

  // أحجام الظلال
  static const double shadowBlurS = 4.0;
  static const double shadowBlurM = 8.0;
  static const double shadowBlurL = 16.0;
  static const double shadowBlurXL = 24.0;

  static const double shadowOffsetS = 2.0;
  static const double shadowOffsetM = 4.0;
  static const double shadowOffsetL = 8.0;
  static const double shadowOffsetXL = 12.0;
}
