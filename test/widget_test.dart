// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:elmokef_app/main.dart';

void main() {
  testWidgets('App starts with splash screen', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const ElmoqefApp());

    // Verify that the splash screen is displayed.
    expect(find.text('الموقف'), findsOneWidget);
    expect(find.text('منصة لربط العملاء بالحرفيين'), findsOneWidget);
    expect(find.text('الإصدار 1.0.0'), findsOneWidget);

    // Verify that the loading indicator is present
    expect(find.byType(CircularProgressIndicator), findsOneWidget);
  });

  testWidgets('Navigation to user type selection after splash',
      (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const ElmoqefApp());

    // Wait for splash screen timer (3 seconds + some buffer)
    await tester.pumpAndSettle(const Duration(seconds: 4));

    // Verify that user type selection screen is displayed
    expect(find.text('مرحباً بك في'), findsOneWidget);
    expect(find.text('اختر نوع حسابك للمتابعة'), findsOneWidget);
    expect(find.text('عميل'), findsOneWidget);
    expect(find.text('حرفي'), findsOneWidget);
  });
}
