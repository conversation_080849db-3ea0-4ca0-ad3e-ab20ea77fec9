import 'package:flutter/material.dart';
import '../../shared/themes/app_theme.dart';
import '../../core/repositories/city_repository.dart';
import '../../core/repositories/craft_repository.dart';
import '../../core/repositories/craftsman_repository.dart';
import '../../core/repositories/customer_repository.dart';
import '../../core/services/auth_service.dart';
import '../../core/models/region_model.dart';
import '../../core/models/city_model.dart';
import '../../core/models/craft_model.dart';
import '../../core/models/craftsman_model.dart';
import '../../core/models/customer_model.dart';
import '../../shared/widgets/region_city_selector.dart';
import '../../shared/widgets/craft_selector.dart';

/// الصفحة الرئيسية للعملاء
class CustomerHomeScreen extends StatefulWidget {
  const CustomerHomeScreen({super.key});

  @override
  State<CustomerHomeScreen> createState() => _CustomerHomeScreenState();
}

class _CustomerHomeScreenState extends State<CustomerHomeScreen> {
  final CityRepository _cityRepository = CityRepository();
  final CraftRepository _craftRepository = CraftRepository();
  final CraftsmanRepository _craftsmanRepository = CraftsmanRepository();
  final CustomerRepository _customerRepository = CustomerRepository();
  final AuthService _authService = AuthService();

  List<Map<String, dynamic>> _featuredCrafts = [];
  List<Map<String, dynamic>> _topCraftsmen = [];
  List<CraftsmanModel> _featuredCraftsmen = [];
  List<RegionModel> _regions = [];
  List<CityModel> _cities = [];
  List<CraftModel> _crafts = [];
  List<CraftsmanModel> _searchResults = [];

  CustomerModel? _currentCustomer;
  String? _selectedRegionId;
  String? _selectedCityId;
  String? _selectedCraftId;

  bool _isLoading = true;
  bool _isSearching = false;
  bool _isLoadingCustomerData = false;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // تحميل البيانات من قاعدة البيانات
      final results = await Future.wait([
        _cityRepository.getAllRegions(),
        _craftRepository.getAllCrafts(),
        _craftsmanRepository.getFeaturedCraftsmen(),
        _loadCustomerDefaultLocation(), // تحميل بيانات العميل
      ]);

      setState(() {
        _regions = results[0] as List<RegionModel>;
        _crafts = results[1] as List<CraftModel>;
        // تحويل النتائج إلى التنسيق المطلوب للعرض
        _featuredCraftsmen = results[2] as List<CraftsmanModel>;
      });

      // تحميل الحرف المميزة (بيانات مؤقتة للعرض)
      _featuredCrafts = [
        {
          'id': 1,
          'name': 'كهربائي',
          'icon': Icons.electrical_services,
          'color': Colors.orange
        },
        {'id': 2, 'name': 'سباك', 'icon': Icons.plumbing, 'color': Colors.blue},
        {
          'id': 3,
          'name': 'نجار',
          'icon': Icons.carpenter,
          'color': Colors.brown
        },
        {
          'id': 4,
          'name': 'حداد',
          'icon': Icons.construction,
          'color': Colors.grey
        },
        {
          'id': 5,
          'name': 'بناء',
          'icon': Icons.home_repair_service,
          'color': Colors.green
        },
        {
          'id': 6,
          'name': 'دهان',
          'icon': Icons.format_paint,
          'color': Colors.purple
        },
      ];

      // تحميل أفضل الحرفيين (بيانات مؤقتة)
      _topCraftsmen = [
        {
          'id': 1,
          'name': 'أحمد محمد',
          'craft': 'كهربائي',
          'rating': 4.8,
          'reviews': 45,
          'city': 'الدار البيضاء'
        },
        {
          'id': 2,
          'name': 'فاطمة علي',
          'craft': 'سباك',
          'rating': 4.6,
          'reviews': 32,
          'city': 'الرباط'
        },
        {
          'id': 3,
          'name': 'يوسف حسن',
          'craft': 'نجار',
          'rating': 4.9,
          'reviews': 67,
          'city': 'فاس'
        },
      ];

      await Future.delayed(const Duration(seconds: 1)); // محاكاة التحميل

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في تحميل البيانات: $e')),
        );
      }
    }
  }

  /// تحميل بيانات العميل وتحديد الموقع الافتراضي
  Future<void> _loadCustomerDefaultLocation() async {
    try {
      setState(() {
        _isLoadingCustomerData = true;
      });

      // الحصول على معرف العميل المسجل
      final currentUser = await _authService.getCurrentUser();
      if (currentUser == null || currentUser.userType != 'customer') {
        debugPrint('⚠️ لا يوجد عميل مسجل');
        return;
      }

      final customerId = currentUser.userId;

      // جلب بيانات العميل الكاملة
      final customer = await _customerRepository.getCustomerById(customerId);
      if (customer == null) {
        debugPrint('⚠️ لم يتم العثور على بيانات العميل');
        return;
      }

      setState(() {
        _currentCustomer = customer;
        _selectedRegionId = customer.regionId;
        _selectedCityId = customer.cityId;
      });

      // تحميل قائمة المدن للجهة المحددة
      if (customer.regionId.isNotEmpty) {
        try {
          final cities =
              await _cityRepository.getCitiesByRegion(customer.regionId);
          setState(() {
            _cities = cities;
          });
          debugPrint(
              '✅ تم تحميل ${cities.length} مدينة للجهة ${customer.regionId}');
        } catch (e) {
          debugPrint('❌ خطأ في تحميل مدن الجهة: $e');
        }
      }

      debugPrint('✅ تم تحميل بيانات العميل: ${customer.name}');
      debugPrint('📍 الجهة: ${customer.regionId}, المدينة: ${customer.cityId}');
    } catch (e) {
      debugPrint('❌ خطأ في تحميل بيانات العميل: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل بيانات الموقع: $e'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoadingCustomerData = false;
      });
    }
  }

  Future<void> _performSearch() async {
    setState(() {
      _isSearching = true;
    });

    try {
      final results = await _craftsmanRepository.searchCraftsmen('');

      setState(() {
        _searchResults = results;
        _isSearching = false;
      });
    } catch (e) {
      setState(() {
        _isSearching = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('خطأ في البحث: $e')),
        );
      }
    }
  }

  void _clearSearch() {
    setState(() {
      _selectedRegionId = null;
      _selectedCityId = null;
      _selectedCraftId = null;
      _searchResults.clear();
    });
  }

  Future<void> _onRegionChanged(String? regionId) async {
    setState(() {
      _selectedRegionId = regionId;
      _selectedCityId = null;
      _cities.clear();
    });

    if (regionId != null) {
      try {
        final cities = await _cityRepository.getCitiesByRegion(regionId);
        setState(() {
          _cities = cities;
        });
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('خطأ في تحميل المدن: $e')),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          'الموقف',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppTheme.primaryColor,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications_outlined, color: Colors.white),
            onPressed: () {
              // TODO: فتح صفحة الإشعارات
            },
          ),
          IconButton(
            icon: const Icon(Icons.search, color: Colors.white),
            onPressed: () {
              // TODO: فتح صفحة البحث
            },
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _loadData,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // قسم الترحيب
                    _buildWelcomeSection(),
                    const SizedBox(height: 24),

                    // بطاقة البحث والفلترة التفاعلية
                    _buildSearchFilterCard(),
                    const SizedBox(height: 24),

                    // عرض نتائج البحث
                    if (_searchResults.isNotEmpty) ...[
                      _buildSearchResultsSection(),
                      const SizedBox(height: 24),
                    ],

                    // شريط البحث القديم (يمكن إزالته لاحقاً)
                    _buildSearchBar(),
                    const SizedBox(height: 24),

                    // الحرف المميزة
                    _buildFeaturedCraftsSection(),
                    const SizedBox(height: 24),

                    // أفضل الحرفيين
                    _buildTopCraftsmenSection(),
                    const SizedBox(height: 24),

                    // إحصائيات سريعة
                    _buildQuickStatsSection(),
                  ],
                ),
              ),
            ),
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        selectedItemColor: AppTheme.primaryColor,
        unselectedItemColor: Colors.grey,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.home),
            label: 'الرئيسية',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.search),
            label: 'البحث',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.favorite_outline),
            label: 'المفضلة',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.person_outline),
            label: 'الملف الشخصي',
          ),
        ],
        onTap: (index) {
          // TODO: التنقل بين الصفحات
        },
      ),
    );
  }

  Widget _buildWelcomeSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppTheme.primaryColor,
            AppTheme.primaryColor.withOpacity(0.8)
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            _currentCustomer != null
                ? 'مرحباً ${_currentCustomer!.name}'
                : 'مرحباً بك في الموقف',
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _currentCustomer != null
                ? 'اعثر على أفضل الحرفيين في منطقتك'
                : 'اعثر على أفضل الحرفيين في المغرب',
            style: const TextStyle(
              fontSize: 16,
              color: Colors.white70,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 6,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: TextField(
        decoration: InputDecoration(
          hintText: 'ابحث عن حرفي أو خدمة...',
          prefixIcon: const Icon(Icons.search, color: AppTheme.primaryColor),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide.none,
          ),
          filled: true,
          fillColor: Colors.white,
        ),
        onTap: () {
          // TODO: فتح صفحة البحث المتقدم
        },
        readOnly: true,
      ),
    );
  }

  Widget _buildFeaturedCraftsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'الحرف المتاحة',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 3,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            childAspectRatio: 1,
          ),
          itemCount: _featuredCrafts.length,
          itemBuilder: (context, index) {
            final craft = _featuredCrafts[index];
            return _buildCraftCard(craft);
          },
        ),
      ],
    );
  }

  Widget _buildCraftCard(Map<String, dynamic> craft) {
    return InkWell(
      onTap: () {
        // TODO: فتح صفحة الحرفيين لهذه الحرفة
      },
      borderRadius: BorderRadius.circular(12),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.1),
              spreadRadius: 1,
              blurRadius: 6,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              craft['icon'],
              size: 32,
              color: craft['color'],
            ),
            const SizedBox(height: 8),
            Text(
              craft['name'],
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTopCraftsmenSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'أفضل الحرفيين',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            TextButton(
              onPressed: () {
                // TODO: عرض جميع الحرفيين
              },
              child: const Text('عرض الكل'),
            ),
          ],
        ),
        const SizedBox(height: 16),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: _topCraftsmen.length,
          itemBuilder: (context, index) {
            final craftsman = _topCraftsmen[index];
            return _buildCraftsmanCard(craftsman);
          },
        ),
      ],
    );
  }

  Widget _buildCraftsmanCard(Map<String, dynamic> craftsman) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 6,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: CircleAvatar(
          radius: 30,
          backgroundColor: AppTheme.primaryColor,
          child: Text(
            craftsman['name'][0],
            style: const TextStyle(
              color: Colors.white,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        title: Text(
          craftsman['name'],
          style: const TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(craftsman['craft']),
            const SizedBox(height: 4),
            Row(
              children: [
                const Icon(Icons.star, color: Colors.amber, size: 16),
                const SizedBox(width: 4),
                Text(
                  '${craftsman['rating']} (${craftsman['reviews']} تقييم)',
                  style: const TextStyle(fontSize: 12),
                ),
              ],
            ),
            Text(
              craftsman['city'],
              style: const TextStyle(
                color: Colors.grey,
                fontSize: 12,
              ),
            ),
          ],
        ),
        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: () {
          // TODO: فتح صفحة تفاصيل الحرفي
        },
      ),
    );
  }

  Widget _buildQuickStatsSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 6,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'إحصائيات سريعة',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildStatItem('الحرفيين', '150+', Icons.person),
              _buildStatItem('المدن', '266', Icons.location_city),
              _buildStatItem('الطلبات', '1000+', Icons.work),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(
          icon,
          size: 32,
          color: AppTheme.primaryColor,
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryColor,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            color: Colors.grey,
          ),
        ),
      ],
    );
  }

  Widget _buildSearchFilterCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 6,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان البطاقة مع معلومات العميل
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'البحث عن حرفيين',
                      style: TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.primaryColor,
                      ),
                    ),
                    if (_currentCustomer != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        'الموقع الحالي: ${_currentCustomer!.name}',
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              if (_isLoadingCustomerData)
                const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
            ],
          ),
          const SizedBox(height: 20),

          // اختيار الجهة والمدينة
          RegionCitySelector(
            selectedRegionId: _selectedRegionId,
            selectedCityId: _selectedCityId,
            regionHint:
                _currentCustomer != null ? 'الجهة الحالية' : 'اختر الجهة',
            cityHint:
                _currentCustomer != null ? 'المدينة الحالية' : 'اختر المدينة',
            onSelectionChanged: (regionId, cityId) {
              setState(() {
                _selectedRegionId = regionId;
                _selectedCityId = cityId;
              });
            },
          ),

          const SizedBox(height: 16),

          // اختيار نوع الحرفة
          CraftSelector(
            selectedCraftId: _selectedCraftId,
            onSelectionChanged: (craftId) {
              setState(() {
                _selectedCraftId = craftId;
              });
            },
          ),

          const SizedBox(height: 20),

          // أزرار البحث والمسح
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _isSearching ? null : _performSearch,
                  icon: _isSearching
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor:
                                AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : const Icon(Icons.search),
                  label: Text(_isSearching ? 'جاري البحث...' : 'بحث'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.primaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              OutlinedButton.icon(
                onPressed: _clearSearch,
                icon: const Icon(Icons.clear_all),
                label: const Text('مسح'),
                style: OutlinedButton.styleFrom(
                  foregroundColor: AppTheme.primaryColor,
                  side: const BorderSide(color: AppTheme.primaryColor),
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSearchResultsSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 6,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'نتائج البحث',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.primaryColor,
                ),
              ),
              Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  '${_searchResults.length} حرفي',
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          if (_searchResults.isEmpty)
            const Center(
              child: Padding(
                padding: EdgeInsets.all(32),
                child: Column(
                  children: [
                    Icon(
                      Icons.search_off,
                      size: 64,
                      color: Colors.grey,
                    ),
                    SizedBox(height: 16),
                    Text(
                      'لم يتم العثور على حرفيين',
                      style: TextStyle(
                        fontSize: 16,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              ),
            )
          else
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: _searchResults.length,
              separatorBuilder: (context, index) => const SizedBox(height: 12),
              itemBuilder: (context, index) {
                final craftsman = _searchResults[index];
                return _buildCraftsmanSearchCard(craftsman);
              },
            ),
        ],
      ),
    );
  }

  Widget _buildCraftsmanSearchCard(CraftsmanModel craftsman) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Row(
        children: [
          CircleAvatar(
            radius: 30,
            backgroundColor: AppTheme.primaryColor,
            child: Text(
              craftsman.name.isNotEmpty ? craftsman.name[0] : 'ح',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  craftsman.name,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  craftsman.email,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    const Icon(Icons.star, color: Colors.amber, size: 16),
                    const SizedBox(width: 4),
                    Text(
                      craftsman.rating.toStringAsFixed(1),
                      style: const TextStyle(fontSize: 14),
                    ),
                    const SizedBox(width: 16),
                    const Icon(Icons.check_circle,
                        color: Colors.green, size: 16),
                    const SizedBox(width: 4),
                    Text(
                      '${craftsman.completedOrders} طلب',
                      style: const TextStyle(fontSize: 14),
                    ),
                  ],
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () {
              // TODO: فتح صفحة تفاصيل الحرفي
            },
            icon: const Icon(Icons.arrow_forward_ios, size: 16),
          ),
        ],
      ),
    );
  }
}
