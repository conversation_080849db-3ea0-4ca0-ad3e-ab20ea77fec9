import '../database/database_helper.dart';
import '../models/customer_model.dart';

/// مستودع العملاء
class CustomerRepository {
  final DatabaseHelper _dbHelper = DatabaseHelper();

  /// إضافة عميل جديد
  Future<int?> insertCustomer(CustomerModel customer) async {
    try {
      final db = await _dbHelper.database;
      final id = await db.insert('customers', customer.toMap());
      return id;
    } catch (e) {
      print('خطأ في إضافة العميل: $e');
      return null;
    }
  }

  /// الحصول على عميل بالبريد الإلكتروني
  Future<CustomerModel?> getCustomerByEmail(String email) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'customers',
        where: 'email = ?',
        whereArgs: [email],
        limit: 1,
      );

      if (maps.isEmpty) return null;
      return CustomerModel.fromMap(maps.first);
    } catch (e) {
      print('خطأ في الحصول على العميل بالبريد الإلكتروني: $e');
      return null;
    }
  }

  /// الحصول على عميل بالمعرف
  Future<CustomerModel?> getCustomerById(int id) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'customers',
        where: 'id = ?',
        whereArgs: [id],
        limit: 1,
      );

      if (maps.isEmpty) return null;
      return CustomerModel.fromMap(maps.first);
    } catch (e) {
      print('خطأ في الحصول على العميل: $e');
      return null;
    }
  }

  /// الحصول على جميع العملاء
  Future<List<CustomerModel>> getAllCustomers() async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'customers',
        orderBy: 'created_at DESC',
      );

      return maps.map((map) => CustomerModel.fromMap(map)).toList();
    } catch (e) {
      print('خطأ في الحصول على العملاء: $e');
      return [];
    }
  }

  /// البحث في العملاء
  Future<List<CustomerModel>> searchCustomers(String query) async {
    try {
      final db = await _dbHelper.database;
      final searchQuery = '%$query%';

      final List<Map<String, dynamic>> maps = await db.query(
        'customers',
        where: 'name LIKE ? OR email LIKE ? OR phone LIKE ?',
        whereArgs: [searchQuery, searchQuery, searchQuery],
        orderBy: 'name ASC',
      );

      return maps.map((map) => CustomerModel.fromMap(map)).toList();
    } catch (e) {
      print('خطأ في البحث في العملاء: $e');
      return [];
    }
  }

  /// الحصول على العملاء حسب الجهة
  Future<List<CustomerModel>> getCustomersByRegion(String regionId) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'customers',
        where: 'region_id = ?',
        whereArgs: [regionId],
        orderBy: 'created_at DESC',
      );

      return maps.map((map) => CustomerModel.fromMap(map)).toList();
    } catch (e) {
      print('خطأ في الحصول على العملاء حسب الجهة: $e');
      return [];
    }
  }

  /// الحصول على العملاء حسب المدينة
  Future<List<CustomerModel>> getCustomersByCity(String cityId) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'customers',
        where: 'city_id = ?',
        whereArgs: [cityId],
        orderBy: 'created_at DESC',
      );

      return maps.map((map) => CustomerModel.fromMap(map)).toList();
    } catch (e) {
      print('خطأ في الحصول على العملاء حسب المدينة: $e');
      return [];
    }
  }

  /// تحديث عميل
  Future<bool> updateCustomer(CustomerModel customer) async {
    try {
      final db = await _dbHelper.database;
      final updatedCustomer = customer.copyWith(
        updatedAt: DateTime.now(),
      );

      final result = await db.update(
        'customers',
        updatedCustomer.toMap(),
        where: 'id = ?',
        whereArgs: [customer.id],
      );

      return result > 0;
    } catch (e) {
      print('خطأ في تحديث العميل: $e');
      return false;
    }
  }

  /// حذف عميل
  Future<bool> deleteCustomer(int id) async {
    try {
      final db = await _dbHelper.database;
      final result = await db.delete(
        'customers',
        where: 'id = ?',
        whereArgs: [id],
      );

      return result > 0;
    } catch (e) {
      print('خطأ في حذف العميل: $e');
      return false;
    }
  }

  /// التحقق من وجود بريد إلكتروني
  Future<bool> emailExists(String email, {int? excludeId}) async {
    try {
      final db = await _dbHelper.database;
      String whereClause = 'email = ?';
      List<dynamic> whereArgs = [email];

      if (excludeId != null) {
        whereClause += ' AND id != ?';
        whereArgs.add(excludeId);
      }

      final List<Map<String, dynamic>> maps = await db.query(
        'customers',
        where: whereClause,
        whereArgs: whereArgs,
        limit: 1,
      );

      return maps.isNotEmpty;
    } catch (e) {
      print('خطأ في التحقق من البريد الإلكتروني: $e');
      return false;
    }
  }

  /// الحصول على إحصائيات العملاء
  Future<Map<String, dynamic>> getCustomersStatistics() async {
    try {
      final db = await _dbHelper.database;

      // إجمالي العملاء
      final totalResult =
          await db.rawQuery('SELECT COUNT(*) as count FROM customers');
      final totalCustomers = totalResult.first['count'] as int;

      // العملاء الجدد هذا الشهر
      final now = DateTime.now();
      final startOfMonth = DateTime(now.year, now.month, 1);
      final newThisMonthResult = await db.rawQuery(
        'SELECT COUNT(*) as count FROM customers WHERE created_at >= ?',
        [startOfMonth.toIso8601String()],
      );
      final newThisMonth = newThisMonthResult.first['count'] as int;

      // العملاء حسب الجهة
      final regionStatsResult = await db.rawQuery('''
        SELECT r.name_ar as region_name, COUNT(c.id) as customer_count
        FROM regions r
        LEFT JOIN customers c ON r.id = c.region_id
        GROUP BY r.id, r.name_ar
        ORDER BY customer_count DESC
      ''');

      return {
        'total_customers': totalCustomers,
        'new_this_month': newThisMonth,
        'customers_by_region': regionStatsResult,
      };
    } catch (e) {
      print('خطأ في الحصول على إحصائيات العملاء: $e');
      return {};
    }
  }

  /// الحصول على العملاء الجدد
  Future<List<CustomerModel>> getRecentCustomers({int limit = 10}) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'customers',
        orderBy: 'created_at DESC',
        limit: limit,
      );

      return maps.map((map) => CustomerModel.fromMap(map)).toList();
    } catch (e) {
      print('خطأ في الحصول على العملاء الجدد: $e');
      return [];
    }
  }
}
