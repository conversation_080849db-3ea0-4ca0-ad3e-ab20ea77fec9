import 'package:flutter/material.dart';
import '../../shared/themes/app_theme.dart';
import '../../core/repositories/customer_repository.dart';
import '../../core/repositories/city_repository.dart';
import '../../core/services/auth_service.dart';
import '../../core/models/customer_model.dart';
import '../../core/models/region_model.dart';
import '../../core/models/city_model.dart';
import '../../shared/widgets/region_city_selector.dart';
import '../../shared/widgets/custom_text_field.dart';
import '../../shared/widgets/custom_button.dart';

/// صفحة الملف الشخصي للعميل
class CustomerProfileScreen extends StatefulWidget {
  const CustomerProfileScreen({super.key});

  @override
  State<CustomerProfileScreen> createState() => _CustomerProfileScreenState();
}

class _CustomerProfileScreenState extends State<CustomerProfileScreen> {
  final CustomerRepository _customerRepository = CustomerRepository();
  final CityRepository _cityRepository = CityRepository();
  final AuthService _authService = AuthService();

  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();

  CustomerModel? _currentCustomer;
  List<RegionModel> _regions = [];
  List<CityModel> _cities = [];

  String? _selectedRegionId;
  String? _selectedCityId;

  bool _isLoading = true;
  bool _isSaving = false;
  bool _hasChanges = false;

  @override
  void initState() {
    super.initState();
    _loadCustomerData();
    _addTextFieldListeners();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  void _addTextFieldListeners() {
    _nameController.addListener(_onFieldChanged);
    _phoneController.addListener(_onFieldChanged);
  }

  void _onFieldChanged() {
    if (!_hasChanges) {
      setState(() {
        _hasChanges = true;
      });
    }
  }

  Future<void> _loadCustomerData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // الحصول على معرف العميل المسجل
      final currentUser = await _authService.getCurrentUser();
      if (currentUser == null || currentUser.userType != 'customer') {
        throw Exception('لا يوجد عميل مسجل');
      }

      // جلب بيانات العميل والجهات
      final results = await Future.wait([
        _customerRepository.getCustomerById(currentUser.userId),
        _cityRepository.getAllRegions(),
      ]);

      final customer = results[0] as CustomerModel?;
      final regions = results[1] as List<RegionModel>;

      if (customer == null) {
        throw Exception('لم يتم العثور على بيانات العميل');
      }

      setState(() {
        _currentCustomer = customer;
        _regions = regions;
        _selectedRegionId = customer.regionId;
        _selectedCityId = customer.cityId;

        // تعبئة الحقول
        _nameController.text = customer.name;
        _emailController.text = customer.email;
        _phoneController.text = customer.phone;
      });

      // تحميل مدن الجهة المحددة
      if (customer.regionId.isNotEmpty) {
        await _loadCitiesForRegion(customer.regionId);
      }

      debugPrint('✅ تم تحميل بيانات العميل: ${customer.name}');
    } catch (e) {
      debugPrint('❌ خطأ في تحميل بيانات العميل: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في تحميل البيانات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _loadCitiesForRegion(String regionId) async {
    try {
      final cities = await _cityRepository.getCitiesByRegion(regionId);
      setState(() {
        _cities = cities;
      });
    } catch (e) {
      debugPrint('❌ خطأ في تحميل مدن الجهة: $e');
    }
  }

  Future<void> _saveChanges() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isSaving = true;
    });

    try {
      final updatedCustomer = _currentCustomer!.copyWith(
        name: _nameController.text.trim(),
        phone: _phoneController.text.trim(),
        regionId: _selectedRegionId ?? '',
        cityId: _selectedCityId ?? '',
      );

      await _customerRepository.updateCustomer(updatedCustomer);

      setState(() {
        _currentCustomer = updatedCustomer;
        _hasChanges = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حفظ التغييرات بنجاح'),
            backgroundColor: Colors.green,
          ),
        );
      }

      debugPrint('✅ تم تحديث بيانات العميل بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في حفظ البيانات: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في حفظ البيانات: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isSaving = false;
      });
    }
  }

  String? _validateName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'الاسم مطلوب';
    }
    if (value.trim().length < 2) {
      return 'الاسم يجب أن يكون أطول من حرفين';
    }
    return null;
  }

  String? _validatePhone(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'رقم الهاتف مطلوب';
    }

    final phoneRegex = RegExp(r'^(\+212|0)[5-7][0-9]{8}$');
    if (!phoneRegex.hasMatch(value.trim())) {
      return 'تنسيق رقم الهاتف المغربي غير صحيح';
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          'الملف الشخصي',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: AppTheme.primaryColor,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildProfileHeader(),
                    const SizedBox(height: 24),
                    _buildPersonalInfoCard(),
                    const SizedBox(height: 16),
                    _buildLocationCard(),
                    const SizedBox(height: 24),
                    _buildSaveButton(),
                    const SizedBox(height: 16),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildProfileHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppTheme.primaryColor,
            AppTheme.primaryColor.withOpacity(0.8)
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Row(
        children: [
          CircleAvatar(
            radius: 40,
            backgroundColor: Colors.white,
            child: Text(
              _currentCustomer?.name.isNotEmpty == true
                  ? _currentCustomer!.name[0].toUpperCase()
                  : 'ع',
              style: TextStyle(
                fontSize: 32,
                fontWeight: FontWeight.bold,
                color: AppTheme.primaryColor,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _currentCustomer?.name ?? 'العميل',
                  style: const TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  _currentCustomer?.email ?? '',
                  style: const TextStyle(
                    fontSize: 16,
                    color: Colors.white70,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPersonalInfoCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 6,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'المعلومات الشخصية',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryColor,
            ),
          ),
          const SizedBox(height: 20),

          // حقل الاسم
          CustomTextField(
            controller: _nameController,
            label: 'الاسم الكامل',
            prefixIcon: Icons.person,
            validator: _validateName,
          ),

          const SizedBox(height: 16),

          // حقل البريد الإلكتروني (للقراءة فقط)
          CustomTextField(
            controller: _emailController,
            label: 'البريد الإلكتروني',
            prefixIcon: Icons.email,
            readOnly: true,
            enabled: false,
          ),

          const SizedBox(height: 16),

          // حقل رقم الهاتف
          CustomTextField(
            controller: _phoneController,
            label: 'رقم الهاتف',
            prefixIcon: Icons.phone,
            keyboardType: TextInputType.phone,
            validator: _validatePhone,
            hint: '+212 6XX XXX XXX',
          ),
        ],
      ),
    );
  }

  Widget _buildLocationCard() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.1),
            spreadRadius: 1,
            blurRadius: 6,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.location_on,
                color: AppTheme.primaryColor,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'معلومات الموقع',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.primaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          RegionCitySelector(
            selectedRegionId: _selectedRegionId,
            selectedCityId: _selectedCityId,
            regionHint: 'اختر الجهة',
            cityHint: 'اختر المدينة',
            onSelectionChanged: (regionId, cityId) {
              setState(() {
                _selectedRegionId = regionId;
                _selectedCityId = cityId;
                _hasChanges = true;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSaveButton() {
    return SizedBox(
      width: double.infinity,
      child: CustomButton(
        text: _isSaving ? 'جاري الحفظ...' : 'حفظ التغييرات',
        onPressed: _hasChanges && !_isSaving ? _saveChanges : null,
        isLoading: _isSaving,
        icon: _isSaving ? null : Icons.save,
      ),
    );
  }
}
