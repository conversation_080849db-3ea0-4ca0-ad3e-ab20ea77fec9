/// إعدادات التطبيق الرئيسية
class AppConfig {
  // معلومات التطبيق
  static const String appName = 'الموقف';
  static const String appNameEn = 'Elmokef';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'تطبيق ربط العملاء بالحرفيين في المغرب';
  
  // إعدادات اللغة والمنطقة
  static const String defaultLanguage = 'ar';
  static const String defaultCountry = 'MA';
  static const List<String> supportedLanguages = ['ar', 'en', 'fr'];
  
  // إعدادات الشبكة
  static const String baseUrl = 'https://api.elmokef.ma';
  static const int connectionTimeout = 30000; // 30 ثانية
  static const int receiveTimeout = 30000; // 30 ثانية
  
  // إعدادات التخزين المحلي
  static const String userTokenKey = 'user_token';
  static const String userDataKey = 'user_data';
  static const String userTypeKey = 'user_type';
  static const String selectedLanguageKey = 'selected_language';
  static const String selectedCityKey = 'selected_city';
  static const String selectedRegionKey = 'selected_region';
  
  // إعدادات الخريطة والموقع
  static const double defaultLatitude = 33.9716; // الرباط
  static const double defaultLongitude = -6.8498; // الرباط
  static const double defaultZoom = 10.0;
  static const double searchRadius = 50.0; // كيلومتر
  
  // إعدادات الصور
  static const int maxImageSize = 5 * 1024 * 1024; // 5 ميجابايت
  static const List<String> allowedImageTypes = ['jpg', 'jpeg', 'png', 'webp'];
  static const int imageQuality = 80;
  
  // إعدادات التقييم
  static const double minRating = 1.0;
  static const double maxRating = 5.0;
  static const double defaultRating = 0.0;
  
  // إعدادات البحث
  static const int searchResultsLimit = 20;
  static const int recentSearchesLimit = 10;
  
  // إعدادات الإشعارات
  static const bool enablePushNotifications = true;
  static const bool enableEmailNotifications = true;
  static const bool enableSmsNotifications = false;
  
  // إعدادات الأمان
  static const int minPasswordLength = 6;
  static const int maxPasswordLength = 50;
  static const int otpLength = 6;
  static const int otpExpiryMinutes = 5;
  
  // إعدادات واجهة المستخدم
  static const Duration animationDuration = Duration(milliseconds: 300);
  static const Duration splashDuration = Duration(seconds: 3);
  static const Duration snackBarDuration = Duration(seconds: 3);
  
  // ألوان التطبيق
  static const int primaryColorValue = 0xFF4CAF50; // أخضر
  static const int customerColorValue = 0xFF1976D2; // أزرق
  static const int errorColorValue = 0xFFE53935; // أحمر
  static const int warningColorValue = 0xFFFF9800; // برتقالي
  static const int successColorValue = 0xFF4CAF50; // أخضر
  
  // روابط مهمة
  static const String privacyPolicyUrl = 'https://elmokef.ma/privacy';
  static const String termsOfServiceUrl = 'https://elmokef.ma/terms';
  static const String supportEmail = '<EMAIL>';
  static const String supportPhone = '+212600000000';
  static const String websiteUrl = 'https://elmokef.ma';
  
  // وسائل التواصل الاجتماعي
  static const String facebookUrl = 'https://facebook.com/elmokef';
  static const String instagramUrl = 'https://instagram.com/elmokef';
  static const String twitterUrl = 'https://twitter.com/elmokef';
  static const String linkedinUrl = 'https://linkedin.com/company/elmokef';
  
  // إعدادات التطوير
  static const bool isDebugMode = true;
  static const bool enableLogging = true;
  static const bool enableAnalytics = false;
  
  // إعدادات الدفع (للمستقبل)
  static const String paymentGateway = 'stripe';
  static const String currency = 'MAD';
  static const double commissionRate = 0.05; // 5%
  
  // إعدادات الملفات
  static const String documentsPath = 'documents';
  static const String imagesPath = 'images';
  static const String cachePath = 'cache';
  
  // إعدادات الأداء
  static const int cacheExpiryHours = 24;
  static const int maxCacheSize = 100 * 1024 * 1024; // 100 ميجابايت
  
  // رسائل النظام
  static const String noInternetMessage = 'لا يوجد اتصال بالإنترنت';
  static const String serverErrorMessage = 'خطأ في الخادم، يرجى المحاولة لاحقاً';
  static const String unknownErrorMessage = 'حدث خطأ غير متوقع';
  static const String successMessage = 'تمت العملية بنجاح';
  
  // إعدادات التحقق
  static const String phoneRegex = r'^(\+212|0)[5-7][0-9]{8}$';
  static const String emailRegex = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$';
  
  // إعدادات الجلسة
  static const int sessionTimeoutMinutes = 30;
  static const int maxLoginAttempts = 5;
  static const int lockoutDurationMinutes = 15;
}
