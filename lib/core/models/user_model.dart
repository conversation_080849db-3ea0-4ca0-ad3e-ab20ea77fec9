/// نموذج المستخدم الأساسي
abstract class UserModel {
  final String id;
  final String email;
  final String firstName;
  final String lastName;
  final String phone;
  final String? profileImage;
  final DateTime createdAt;
  final DateTime updatedAt;
  final bool isActive;
  final bool isVerified;

  const UserModel({
    required this.id,
    required this.email,
    required this.firstName,
    required this.lastName,
    required this.phone,
    this.profileImage,
    required this.createdAt,
    required this.updatedAt,
    this.isActive = true,
    this.isVerified = false,
  });

  String get fullName => '$firstName $lastName';

  Map<String, dynamic> toMap();

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// نموذج العميل
class CustomerModel extends UserModel {
  final String? address;
  final double? latitude;
  final double? longitude;
  final String cityId;
  final List<String> favoriteServices;
  final int totalOrders;
  final double averageRating;

  const CustomerModel({
    required super.id,
    required super.email,
    required super.firstName,
    required super.lastName,
    required super.phone,
    super.profileImage,
    required super.createdAt,
    required super.updatedAt,
    super.isActive,
    super.isVerified,
    this.address,
    this.latitude,
    this.longitude,
    required this.cityId,
    this.favoriteServices = const [],
    this.totalOrders = 0,
    this.averageRating = 0.0,
  });

  factory CustomerModel.fromMap(Map<String, dynamic> map) {
    return CustomerModel(
      id: map['id'] ?? '',
      email: map['email'] ?? '',
      firstName: map['firstName'] ?? '',
      lastName: map['lastName'] ?? '',
      phone: map['phone'] ?? '',
      profileImage: map['profileImage'],
      createdAt: DateTime.parse(map['createdAt']),
      updatedAt: DateTime.parse(map['updatedAt']),
      isActive: map['isActive'] ?? true,
      isVerified: map['isVerified'] ?? false,
      address: map['address'],
      latitude: map['latitude']?.toDouble(),
      longitude: map['longitude']?.toDouble(),
      cityId: map['cityId'] ?? '',
      favoriteServices: List<String>.from(map['favoriteServices'] ?? []),
      totalOrders: map['totalOrders'] ?? 0,
      averageRating: map['averageRating']?.toDouble() ?? 0.0,
    );
  }

  @override
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'email': email,
      'firstName': firstName,
      'lastName': lastName,
      'phone': phone,
      'profileImage': profileImage,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'isActive': isActive,
      'isVerified': isVerified,
      'address': address,
      'latitude': latitude,
      'longitude': longitude,
      'cityId': cityId,
      'favoriteServices': favoriteServices,
      'totalOrders': totalOrders,
      'averageRating': averageRating,
    };
  }

  CustomerModel copyWith({
    String? id,
    String? email,
    String? firstName,
    String? lastName,
    String? phone,
    String? profileImage,
    DateTime? createdAt,
    DateTime? updatedAt,
    bool? isActive,
    bool? isVerified,
    String? address,
    double? latitude,
    double? longitude,
    String? cityId,
    List<String>? favoriteServices,
    int? totalOrders,
    double? averageRating,
  }) {
    return CustomerModel(
      id: id ?? this.id,
      email: email ?? this.email,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      phone: phone ?? this.phone,
      profileImage: profileImage ?? this.profileImage,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      isActive: isActive ?? this.isActive,
      isVerified: isVerified ?? this.isVerified,
      address: address ?? this.address,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      cityId: cityId ?? this.cityId,
      favoriteServices: favoriteServices ?? this.favoriteServices,
      totalOrders: totalOrders ?? this.totalOrders,
      averageRating: averageRating ?? this.averageRating,
    );
  }
}
