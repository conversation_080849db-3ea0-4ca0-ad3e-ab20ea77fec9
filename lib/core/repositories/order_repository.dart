import '../database/database_helper.dart';
import '../models/order_model.dart';

/// مستودع الطلبات
class OrderRepository {
  final DatabaseHelper _dbHelper = DatabaseHelper();

  /// إضافة طلب جديد
  Future<int?> insertOrder(OrderModel order) async {
    try {
      final db = await _dbHelper.database;
      final id = await db.insert('orders', order.toMap());
      return id;
    } catch (e) {
      print('خطأ في إضافة الطلب: $e');
      return null;
    }
  }

  /// الحصول على طلب بالمعرف
  Future<OrderModel?> getOrderById(int id) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'orders',
        where: 'id = ?',
        whereArgs: [id],
        limit: 1,
      );

      if (maps.isEmpty) return null;
      return OrderModel.fromMap(maps.first);
    } catch (e) {
      print('خطأ في الحصول على الطلب: $e');
      return null;
    }
  }

  /// الحصول على جميع الطلبات
  Future<List<OrderModel>> getAllOrders() async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'orders',
        orderBy: 'created_at DESC',
      );

      return maps.map((map) => OrderModel.fromMap(map)).toList();
    } catch (e) {
      print('خطأ في الحصول على الطلبات: $e');
      return [];
    }
  }

  /// الحصول على طلبات العميل
  Future<List<OrderModel>> getCustomerOrders(int customerId) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'orders',
        where: 'customer_id = ?',
        whereArgs: [customerId],
        orderBy: 'created_at DESC',
      );

      return maps.map((map) => OrderModel.fromMap(map)).toList();
    } catch (e) {
      print('خطأ في الحصول على طلبات العميل: $e');
      return [];
    }
  }

  /// الحصول على طلبات الحرفي
  Future<List<OrderModel>> getCraftsmanOrders(int craftsmanId) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'orders',
        where: 'craftsman_id = ?',
        whereArgs: [craftsmanId],
        orderBy: 'created_at DESC',
      );

      return maps.map((map) => OrderModel.fromMap(map)).toList();
    } catch (e) {
      print('خطأ في الحصول على طلبات الحرفي: $e');
      return [];
    }
  }

  /// الحصول على الطلبات حسب الحالة
  Future<List<OrderModel>> getOrdersByStatus(OrderStatus status) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'orders',
        where: 'status = ?',
        whereArgs: [status.name],
        orderBy: 'created_at DESC',
      );

      return maps.map((map) => OrderModel.fromMap(map)).toList();
    } catch (e) {
      print('خطأ في الحصول على الطلبات حسب الحالة: $e');
      return [];
    }
  }

  /// الحصول على طلبات العميل حسب الحالة
  Future<List<OrderModel>> getCustomerOrdersByStatus(
    int customerId,
    OrderStatus status,
  ) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'orders',
        where: 'customer_id = ? AND status = ?',
        whereArgs: [customerId, status.name],
        orderBy: 'created_at DESC',
      );

      return maps.map((map) => OrderModel.fromMap(map)).toList();
    } catch (e) {
      print('خطأ في الحصول على طلبات العميل حسب الحالة: $e');
      return [];
    }
  }

  /// الحصول على طلبات الحرفي حسب الحالة
  Future<List<OrderModel>> getCraftsmanOrdersByStatus(
    int craftsmanId,
    OrderStatus status,
  ) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'orders',
        where: 'craftsman_id = ? AND status = ?',
        whereArgs: [craftsmanId, status.name],
        orderBy: 'created_at DESC',
      );

      return maps.map((map) => OrderModel.fromMap(map)).toList();
    } catch (e) {
      print('خطأ في الحصول على طلبات الحرفي حسب الحالة: $e');
      return [];
    }
  }

  /// تحديث طلب
  Future<bool> updateOrder(OrderModel order) async {
    try {
      final db = await _dbHelper.database;
      final updatedOrder = order.copyWith(
        updatedAt: DateTime.now(),
      );
      
      final result = await db.update(
        'orders',
        updatedOrder.toMap(),
        where: 'id = ?',
        whereArgs: [order.id],
      );
      
      return result > 0;
    } catch (e) {
      print('خطأ في تحديث الطلب: $e');
      return false;
    }
  }

  /// تحديث حالة الطلب
  Future<bool> updateOrderStatus(int orderId, OrderStatus status) async {
    try {
      final db = await _dbHelper.database;
      
      Map<String, dynamic> updateData = {
        'status': status.name,
        'updated_at': DateTime.now().toIso8601String(),
      };
      
      // إضافة تاريخ الإكمال إذا كانت الحالة مكتملة
      if (status == OrderStatus.completed) {
        updateData['completed_at'] = DateTime.now().toIso8601String();
      }
      
      final result = await db.update(
        'orders',
        updateData,
        where: 'id = ?',
        whereArgs: [orderId],
      );
      
      return result > 0;
    } catch (e) {
      print('خطأ في تحديث حالة الطلب: $e');
      return false;
    }
  }

  /// إضافة تقييم للطلب
  Future<bool> addOrderRating(
    int orderId,
    double rating,
    String? review,
  ) async {
    try {
      final db = await _dbHelper.database;
      
      final result = await db.update(
        'orders',
        {
          'customer_rating': rating,
          'customer_review': review,
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ? AND status = ?',
        whereArgs: [orderId, OrderStatus.completed.name],
      );
      
      return result > 0;
    } catch (e) {
      print('خطأ في إضافة تقييم الطلب: $e');
      return false;
    }
  }

  /// حذف طلب
  Future<bool> deleteOrder(int id) async {
    try {
      final db = await _dbHelper.database;
      final result = await db.delete(
        'orders',
        where: 'id = ?',
        whereArgs: [id],
      );
      
      return result > 0;
    } catch (e) {
      print('خطأ في حذف الطلب: $e');
      return false;
    }
  }

  /// البحث في الطلبات
  Future<List<OrderModel>> searchOrders(String query) async {
    try {
      final db = await _dbHelper.database;
      final searchQuery = '%$query%';
      
      final List<Map<String, dynamic>> maps = await db.query(
        'orders',
        where: 'title LIKE ? OR description LIKE ?',
        whereArgs: [searchQuery, searchQuery],
        orderBy: 'created_at DESC',
      );

      return maps.map((map) => OrderModel.fromMap(map)).toList();
    } catch (e) {
      print('خطأ في البحث في الطلبات: $e');
      return [];
    }
  }

  /// الحصول على إحصائيات الطلبات
  Future<Map<String, dynamic>> getOrdersStatistics() async {
    try {
      final db = await _dbHelper.database;
      
      // إجمالي الطلبات
      final totalResult = await db.rawQuery('SELECT COUNT(*) as count FROM orders');
      final totalOrders = totalResult.first['count'] as int;
      
      // الطلبات حسب الحالة
      final statusStats = <String, int>{};
      for (final status in OrderStatus.values) {
        final result = await db.rawQuery(
          'SELECT COUNT(*) as count FROM orders WHERE status = ?',
          [status.name],
        );
        statusStats[status.name] = result.first['count'] as int;
      }
      
      // الطلبات الجديدة هذا الشهر
      final now = DateTime.now();
      final startOfMonth = DateTime(now.year, now.month, 1);
      final newThisMonthResult = await db.rawQuery(
        'SELECT COUNT(*) as count FROM orders WHERE created_at >= ?',
        [startOfMonth.toIso8601String()],
      );
      final newThisMonth = newThisMonthResult.first['count'] as int;
      
      // متوسط التقييم
      final avgRatingResult = await db.rawQuery(
        'SELECT AVG(customer_rating) as avg_rating FROM orders WHERE customer_rating IS NOT NULL',
      );
      final avgRating = (avgRatingResult.first['avg_rating'] as double?) ?? 0.0;
      
      return {
        'total_orders': totalOrders,
        'status_breakdown': statusStats,
        'new_this_month': newThisMonth,
        'average_rating': avgRating,
      };
    } catch (e) {
      print('خطأ في الحصول على إحصائيات الطلبات: $e');
      return {};
    }
  }

  /// الحصول على الطلبات الحديثة
  Future<List<OrderModel>> getRecentOrders({int limit = 10}) async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.query(
        'orders',
        orderBy: 'created_at DESC',
        limit: limit,
      );

      return maps.map((map) => OrderModel.fromMap(map)).toList();
    } catch (e) {
      print('خطأ في الحصول على الطلبات الحديثة: $e');
      return [];
    }
  }

  /// الحصول على الطلبات مع تفاصيل العميل والحرفي
  Future<List<Map<String, dynamic>>> getOrdersWithDetails() async {
    try {
      final db = await _dbHelper.database;
      final List<Map<String, dynamic>> maps = await db.rawQuery('''
        SELECT 
          o.*,
          c.name as customer_name,
          c.phone as customer_phone,
          cm.name as craftsman_name,
          cm.phone as craftsman_phone,
          cr.name_ar as craft_name
        FROM orders o
        LEFT JOIN customers c ON o.customer_id = c.id
        LEFT JOIN craftsmen cm ON o.craftsman_id = cm.id
        LEFT JOIN crafts cr ON cm.craft_id = cr.id
        ORDER BY o.created_at DESC
      ''');

      return maps;
    } catch (e) {
      print('خطأ في الحصول على الطلبات مع التفاصيل: $e');
      return [];
    }
  }
}
