import 'package:flutter/foundation.dart';
import '../database/database_helper.dart';
import '../repositories/customer_repository.dart';
import '../repositories/craftsman_repository.dart';
import '../repositories/order_repository.dart';
import '../models/customer_model.dart';
import '../models/craftsman_model.dart';
import '../../quick_reset.dart';

/// خدمة قاعدة البيانات الرئيسية
class DatabaseService {
  static final DatabaseService _instance = DatabaseService._internal();
  factory DatabaseService() => _instance;
  DatabaseService._internal();

  final DatabaseHelper _dbHelper = DatabaseHelper();
  final CustomerRepository _customerRepository = CustomerRepository();
  final CraftsmanRepository _craftsmanRepository = CraftsmanRepository();
  final OrderRepository _orderRepository = OrderRepository();

  bool _isInitialized = false;

  /// تهيئة قاعدة البيانات
  Future<void> initialize() async {
    if (_isInitialized) {
      debugPrint('✅ قاعدة البيانات مهيأة مسبقاً');
      return;
    }

    try {
      debugPrint('🚀 بدء تهيئة قاعدة البيانات...');

      // فحص وإعادة تعيين قاعدة البيانات إذا لزم الأمر
      await QuickDatabaseReset.resetIfNeeded();

      // تهيئة قاعدة البيانات
      await _dbHelper.database;

      // التحقق من وجود البيانات الأولية
      await _verifyInitialData();

      _isInitialized = true;
      debugPrint('✅ تم تهيئة قاعدة البيانات بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة قاعدة البيانات: $e');
      rethrow;
    }
  }

  /// التحقق من وجود البيانات الأولية
  Future<void> _verifyInitialData() async {
    try {
      final db = await _dbHelper.database;

      // التحقق من وجود الجهات
      final regionsCount =
          await db.rawQuery('SELECT COUNT(*) as count FROM regions');
      final regionsTotal = regionsCount.first['count'] as int;

      // التحقق من وجود المدن
      final citiesCount =
          await db.rawQuery('SELECT COUNT(*) as count FROM cities');
      final citiesTotal = citiesCount.first['count'] as int;

      // التحقق من وجود الحرف
      final craftsCount =
          await db.rawQuery('SELECT COUNT(*) as count FROM crafts');
      final craftsTotal = craftsCount.first['count'] as int;

      debugPrint('📊 إحصائيات البيانات الأولية:');
      debugPrint('   - الجهات: $regionsTotal');
      debugPrint('   - المدن: $citiesTotal');
      debugPrint('   - الحرف: $craftsTotal');

      if (regionsTotal == 0 || citiesTotal == 0 || craftsTotal == 0) {
        debugPrint('⚠️ بعض البيانات الأولية مفقودة، سيتم إعادة إدراجها');
        // يمكن إضافة منطق إعادة إدراج البيانات هنا
      }
    } catch (e) {
      debugPrint('❌ خطأ في التحقق من البيانات الأولية: $e');
    }
  }

  /// الحصول على إحصائيات عامة
  Future<Map<String, dynamic>> getGeneralStatistics() async {
    try {
      // إحصائيات العملاء
      final customersStats = await _customerRepository.getCustomersStatistics();

      // إحصائيات الحرفيين
      final craftsmenStats =
          await _craftsmanRepository.getCraftsmenStatistics();

      // إحصائيات الطلبات
      final ordersStats = await _orderRepository.getOrdersStatistics();

      return {
        'customers': customersStats,
        'craftsmen': craftsmenStats,
        'orders': ordersStats,
        'last_updated': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على الإحصائيات العامة: $e');
      return {};
    }
  }

  /// البحث الشامل
  Future<Map<String, dynamic>> globalSearch(String query) async {
    try {
      if (query.trim().isEmpty) {
        return {
          'customers': <CustomerModel>[],
          'craftsmen': <CraftsmanModel>[],
        };
      }

      // البحث في العملاء
      final customers = await _customerRepository.searchCustomers(query);

      // البحث في الحرفيين
      final craftsmen = await _craftsmanRepository.searchCraftsmen(query);

      return {
        'customers': customers,
        'craftsmen': craftsmen,
        'total_results': customers.length + craftsmen.length,
      };
    } catch (e) {
      debugPrint('❌ خطأ في البحث الشامل: $e');
      return {
        'customers': <CustomerModel>[],
        'craftsmen': <CraftsmanModel>[],
        'total_results': 0,
      };
    }
  }

  /// تنظيف قاعدة البيانات
  Future<void> cleanupDatabase() async {
    try {
      final db = await _dbHelper.database;

      // حذف الطلبات الملغية القديمة (أكثر من 30 يوم)
      final thirtyDaysAgo = DateTime.now().subtract(const Duration(days: 30));
      await db.delete(
        'orders',
        where: 'status = ? AND created_at < ?',
        whereArgs: ['cancelled', thirtyDaysAgo.toIso8601String()],
      );

      debugPrint('🧹 تم تنظيف قاعدة البيانات');
    } catch (e) {
      debugPrint('❌ خطأ في تنظيف قاعدة البيانات: $e');
    }
  }

  /// نسخ احتياطي من البيانات المهمة
  Future<Map<String, dynamic>> exportData() async {
    try {
      final db = await _dbHelper.database;

      // تصدير العملاء
      final customersData = await db.query('customers');

      // تصدير الحرفيين
      final craftsmenData = await db.query('craftsmen');

      // تصدير الطلبات
      final ordersData = await db.query('orders');

      return {
        'export_date': DateTime.now().toIso8601String(),
        'customers': customersData,
        'craftsmen': craftsmenData,
        'orders': ordersData,
      };
    } catch (e) {
      debugPrint('❌ خطأ في تصدير البيانات: $e');
      return {};
    }
  }

  /// إعادة تعيين قاعدة البيانات (للاختبار فقط)
  Future<void> resetDatabase() async {
    try {
      await _dbHelper.deleteDatabase();
      _isInitialized = false;
      await initialize();
      debugPrint('🔄 تم إعادة تعيين قاعدة البيانات');
    } catch (e) {
      debugPrint('❌ خطأ في إعادة تعيين قاعدة البيانات: $e');
      rethrow;
    }
  }

  /// إغلاق قاعدة البيانات
  Future<void> close() async {
    try {
      await _dbHelper.close();
      _isInitialized = false;
      debugPrint('🔒 تم إغلاق قاعدة البيانات');
    } catch (e) {
      debugPrint('❌ خطأ في إغلاق قاعدة البيانات: $e');
    }
  }

  /// الحصول على حالة قاعدة البيانات
  bool get isInitialized => _isInitialized;

  /// الحصول على مستودع العملاء
  CustomerRepository get customerRepository => _customerRepository;

  /// الحصول على مستودع الحرفيين
  CraftsmanRepository get craftsmanRepository => _craftsmanRepository;

  /// الحصول على مستودع الطلبات
  OrderRepository get orderRepository => _orderRepository;

  /// الحصول على مساعد قاعدة البيانات
  DatabaseHelper get databaseHelper => _dbHelper;

  /// إعادة تعيين قاعدة البيانات فوراً
  Future<void> forceReset() async {
    try {
      debugPrint('🔄 بدء إعادة التعيين الفوري...');

      // إعادة تعيين حالة التهيئة
      _isInitialized = false;

      // حذف قاعدة البيانات القديمة
      await _dbHelper.deleteDatabase();
      debugPrint('🗑️ تم حذف قاعدة البيانات القديمة');

      // إعادة تهيئة قاعدة البيانات
      await initialize();
      debugPrint('✅ تم إعادة تعيين قاعدة البيانات بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في إعادة التعيين الفوري: $e');
      rethrow;
    }
  }
}
