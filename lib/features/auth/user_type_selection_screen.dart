import 'package:flutter/material.dart';
import '../../shared/themes/app_theme.dart';
import '../../shared/constants/app_dimensions.dart';
import '../../shared/constants/app_colors.dart';
import '../../core/constants/app_constants.dart';
import '../../shared/widgets/custom_button.dart';
import '../../shared/widgets/custom_card.dart';

import 'customer_login_screen.dart';
import 'craftsman_login_screen.dart';
import '../../core/services/database_service.dart';

class UserTypeSelectionScreen extends StatefulWidget {
  const UserTypeSelectionScreen({super.key});

  @override
  State<UserTypeSelectionScreen> createState() =>
      _UserTypeSelectionScreenState();
}

class _UserTypeSelectionScreenState extends State<UserTypeSelectionScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  String? _selectedUserType;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _selectUserType(String userType) {
    setState(() {
      _selectedUserType = userType;
    });
  }

  void _continueToNextScreen() {
    if (_selectedUserType == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('يرجى اختيار نوع المستخدم'),
          backgroundColor: AppTheme.errorColor,
        ),
      );
      return;
    }

    // التنقل إلى صفحة تسجيل الدخول المناسبة
    if (_selectedUserType == AppConstants.userTypeCustomer) {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => const CustomerLoginScreen(),
        ),
      );
    } else if (_selectedUserType == AppConstants.userTypeCraftsman) {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => const CraftsmanLoginScreen(),
        ),
      );
    }
  }

  /// إعادة تعيين فوري لقاعدة البيانات
  Future<void> _quickReset() async {
    try {
      // عرض مؤشر التحميل
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('جاري إعادة تعيين قاعدة البيانات...'),
            ],
          ),
        ),
      );

      // إعادة تعيين قاعدة البيانات
      final dbService = DatabaseService();
      await dbService.forceReset();

      // إغلاق مؤشر التحميل
      if (mounted) {
        Navigator.of(context).pop();

        // عرض رسالة نجاح
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text(
                'تم إعادة تعيين قاعدة البيانات بنجاح! الآن يمكنك التسجيل.'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      // إغلاق مؤشر التحميل
      if (mounted) {
        Navigator.of(context).pop();

        // عرض رسالة خطأ
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إعادة التعيين: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFFF5F5F5),
              Color(0xFFE8F5E8),
            ],
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: FadeTransition(
              opacity: _fadeAnimation,
              child: SlideTransition(
                position: _slideAnimation,
                child: SingleChildScrollView(
                  child: ConstrainedBox(
                    constraints: BoxConstraints(
                      minHeight: MediaQuery.of(context).size.height -
                          MediaQuery.of(context).padding.top -
                          MediaQuery.of(context).padding.bottom -
                          48, // padding
                    ),
                    child: Column(
                      children: [
                        // العنوان الرئيسي
                        const SizedBox(height: 40),
                        Text(
                          'مرحباً بك في',
                          style: Theme.of(context)
                              .textTheme
                              .headlineMedium
                              ?.copyWith(
                                color: AppTheme.textSecondary,
                                fontWeight: FontWeight.w400,
                              ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'الموقف',
                          style: Theme.of(context)
                              .textTheme
                              .displayMedium
                              ?.copyWith(
                                color: AppTheme.primaryColor,
                                fontWeight: FontWeight.bold,
                              ),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'اختر نوع حسابك للمتابعة',
                          style:
                              Theme.of(context).textTheme.bodyLarge?.copyWith(
                                    color: AppTheme.textSecondary,
                                  ),
                          textAlign: TextAlign.center,
                        ),

                        const SizedBox(height: 40),

                        // خيار العميل
                        _buildUserTypeCard(
                          userType: AppConstants.userTypeCustomer,
                          title: 'عميل',
                          subtitle: 'أبحث عن حرفيين ومقدمي خدمات',
                          icon: Icons.person_outline,
                          color: AppColors.infoColor,
                        ),

                        const SizedBox(height: AppDimensions.sectionSpacing),

                        // خيار الحرفي
                        _buildUserTypeCard(
                          userType: AppConstants.userTypeCraftsman,
                          title: 'حرفي',
                          subtitle: 'أقدم خدمات حرفية ومهنية',
                          icon: Icons.handyman_outlined,
                          color: AppTheme.primaryColor,
                        ),

                        const SizedBox(height: 40),

                        // زر المتابعة
                        PrimaryButton(
                          text: 'متابعة',
                          onPressed: _selectedUserType != null
                              ? _continueToNextScreen
                              : null,
                          width: double.infinity,
                          icon: Icons.arrow_forward,
                        ),

                        const SizedBox(height: 40),

                        // زر إعادة تعيين قاعدة البيانات (للتطوير فقط)
                        TextButton(
                          onPressed: _quickReset,
                          child: const Text(
                            'إعادة تعيين قاعدة البيانات',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.orange,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildUserTypeCard({
    required String userType,
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
  }) {
    final bool isSelected = _selectedUserType == userType;

    return CustomCard(
      onTap: () => _selectUserType(userType),
      isClickable: true,
      backgroundColor: isSelected ? color.withOpacity(0.1) : Colors.white,
      border: isSelected
          ? Border.all(color: color, width: 2)
          : Border.all(color: Colors.grey.shade300, width: 1),
      boxShadow: [
        BoxShadow(
          color: isSelected
              ? color.withOpacity(0.3)
              : Colors.black.withOpacity(0.08),
          blurRadius: isSelected ? 16 : 8,
          offset: Offset(0, isSelected ? 8 : 4),
        ),
      ],
      child: Column(
        children: [
          // الأيقونة مع تحسينات بصرية
          AnimatedContainer(
            duration: AppDimensions.animationMedium,
            width: AppDimensions.avatarXL,
            height: AppDimensions.avatarXL,
            decoration: BoxDecoration(
              color: isSelected ? color : color.withOpacity(0.1),
              shape: BoxShape.circle,
              boxShadow: isSelected
                  ? [
                      BoxShadow(
                        color: color.withOpacity(0.4),
                        blurRadius: 12,
                        offset: const Offset(0, 4),
                      ),
                    ]
                  : null,
            ),
            child: Icon(
              icon,
              size: AppDimensions.iconXL,
              color: isSelected ? Colors.white : color,
            ),
          ),

          const SizedBox(height: AppDimensions.paddingL),

          // العنوان
          Text(
            title,
            style: TextStyle(
              fontSize: AppDimensions.fontTitle,
              fontWeight: FontWeight.bold,
              color: isSelected ? color : AppTheme.textPrimary,
            ),
          ),

          const SizedBox(height: AppDimensions.paddingS),

          // الوصف
          Text(
            subtitle,
            style: const TextStyle(
              fontSize: AppDimensions.fontL,
              color: AppTheme.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),

          // علامة الاختيار مع رسوم متحركة
          if (isSelected) ...[
            const SizedBox(height: 12),
            Icon(
              Icons.check_circle,
              color: color,
              size: 28,
            ),
          ],
        ],
      ),
    );
  }
}
