import 'package:flutter/material.dart';
import 'core/services/database_service.dart';

/// أداة فحص شاملة لبيانات المدن المغربية
class CitiesDataChecker {
  
  /// فحص شامل لبيانات المدن
  static Future<Map<String, dynamic>> checkCitiesData() async {
    try {
      final dbService = DatabaseService();
      await dbService.initialize();
      
      final dbHelper = dbService.databaseHelper;
      final db = await dbHelper.database;
      
      // 1. العدد الإجمالي للمدن
      final totalCitiesResult = await db.rawQuery('SELECT COUNT(*) as count FROM cities');
      final totalCities = totalCitiesResult.first['count'] as int;
      
      // 2. العدد الإجمالي للجهات
      final totalRegionsResult = await db.rawQuery('SELECT COUNT(*) as count FROM regions');
      final totalRegions = totalRegionsResult.first['count'] as int;
      
      // 3. توزيع المدن على الجهات
      final citiesPerRegionResult = await db.rawQuery('''
        SELECT 
          r.id as region_id,
          r.name_ar as region_name,
          COUNT(c.id) as cities_count
        FROM regions r
        LEFT JOIN cities c ON r.id = c.region_id
        GROUP BY r.id, r.name_ar
        ORDER BY r.id
      ''');
      
      // 4. فحص اكتمال البيانات
      final incompleteCitiesResult = await db.rawQuery('''
        SELECT 
          id,
          name_ar,
          name_fr,
          name_en,
          region_id,
          CASE 
            WHEN name_ar IS NULL OR name_ar = '' THEN 'missing_name_ar'
            WHEN name_fr IS NULL OR name_fr = '' THEN 'missing_name_fr'
            WHEN name_en IS NULL OR name_en = '' THEN 'missing_name_en'
            WHEN region_id IS NULL OR region_id = '' THEN 'missing_region_id'
            ELSE 'complete'
          END as status
        FROM cities
        WHERE name_ar IS NULL OR name_ar = '' 
           OR name_fr IS NULL OR name_fr = ''
           OR name_en IS NULL OR name_en = ''
           OR region_id IS NULL OR region_id = ''
      ''');
      
      // 5. فحص المعرفات العالية المطلوبة
      final highIdCitiesResult = await db.rawQuery('''
        SELECT id, name_ar, region_id
        FROM cities 
        WHERE id IN (80, 178, 200, 266)
        ORDER BY id
      ''');
      
      // 6. فحص أعلى وأقل معرف
      final minMaxIdResult = await db.rawQuery('''
        SELECT 
          MIN(CAST(id AS INTEGER)) as min_id,
          MAX(CAST(id AS INTEGER)) as max_id
        FROM cities
      ''');
      
      // 7. فحص المدن المكررة (نفس الاسم في نفس الجهة)
      final duplicateCitiesResult = await db.rawQuery('''
        SELECT 
          name_ar,
          region_id,
          COUNT(*) as count
        FROM cities
        GROUP BY name_ar, region_id
        HAVING COUNT(*) > 1
      ''');
      
      // 8. فحص الجهات بدون مدن
      final regionsWithoutCitiesResult = await db.rawQuery('''
        SELECT 
          r.id,
          r.name_ar
        FROM regions r
        LEFT JOIN cities c ON r.id = c.region_id
        WHERE c.id IS NULL
      ''');
      
      // 9. عينة من المدن لكل جهة
      final sampleCitiesResult = await db.rawQuery('''
        SELECT 
          c.id,
          c.name_ar,
          c.name_fr,
          c.name_en,
          c.region_id,
          r.name_ar as region_name
        FROM cities c
        JOIN regions r ON c.region_id = r.id
        ORDER BY c.region_id, c.id
        LIMIT 50
      ''');
      
      return {
        'status': 'success',
        'timestamp': DateTime.now().toIso8601String(),
        'summary': {
          'total_cities': totalCities,
          'total_regions': totalRegions,
          'target_cities': 266, // الهدف الأصلي
          'current_target': 200, // الهدف الحالي
          'completion_percentage': (totalCities / 266 * 100).round(),
          'current_completion_percentage': (totalCities / 200 * 100).round(),
        },
        'distribution': {
          'cities_per_region': citiesPerRegionResult,
          'regions_without_cities': regionsWithoutCitiesResult,
        },
        'data_quality': {
          'incomplete_cities': incompleteCitiesResult,
          'duplicate_cities': duplicateCitiesResult,
          'incomplete_count': incompleteCitiesResult.length,
          'duplicate_count': duplicateCitiesResult.length,
        },
        'id_analysis': {
          'min_max': minMaxIdResult.first,
          'high_id_cities': highIdCitiesResult,
          'missing_high_ids': _findMissingHighIds(highIdCitiesResult),
        },
        'sample_data': sampleCitiesResult,
      };
      
    } catch (e) {
      return {
        'status': 'error',
        'error': e.toString(),
        'timestamp': DateTime.now().toIso8601String(),
      };
    }
  }
  
  /// البحث عن المعرفات العالية المفقودة
  static List<int> _findMissingHighIds(List<Map<String, dynamic>> existingHighIds) {
    final requiredIds = [80, 178, 200, 266];
    final existingIds = existingHighIds.map((city) => int.parse(city['id'].toString())).toList();
    return requiredIds.where((id) => !existingIds.contains(id)).toList();
  }
  
  /// طباعة تقرير مفصل
  static Future<void> printDetailedReport() async {
    debugPrint('🏙️ بدء فحص شامل لبيانات المدن المغربية...');
    debugPrint('=' * 60);
    
    final result = await checkCitiesData();
    
    if (result['status'] == 'error') {
      debugPrint('❌ خطأ في فحص بيانات المدن: ${result['error']}');
      return;
    }
    
    final summary = result['summary'];
    final distribution = result['distribution'];
    final dataQuality = result['data_quality'];
    final idAnalysis = result['id_analysis'];
    
    // 1. الملخص العام
    debugPrint('\n📊 الملخص العام:');
    debugPrint('   العدد الحالي للمدن: ${summary['total_cities']}');
    debugPrint('   الهدف الأصلي: ${summary['target_cities']} مدينة');
    debugPrint('   الهدف الحالي: ${summary['current_target']} مدينة');
    debugPrint('   نسبة الإكمال من الهدف الأصلي: ${summary['completion_percentage']}%');
    debugPrint('   نسبة الإكمال من الهدف الحالي: ${summary['current_completion_percentage']}%');
    debugPrint('   عدد الجهات: ${summary['total_regions']}');
    
    // 2. التوزيع الجغرافي
    debugPrint('\n🗺️ التوزيع الجغرافي:');
    final citiesPerRegion = distribution['cities_per_region'] as List;
    for (final region in citiesPerRegion) {
      final regionName = region['region_name'];
      final citiesCount = region['cities_count'];
      final status = citiesCount > 0 ? '✅' : '❌';
      debugPrint('   $status $regionName: $citiesCount مدينة');
    }
    
    // 3. الجهات بدون مدن
    final regionsWithoutCities = distribution['regions_without_cities'] as List;
    if (regionsWithoutCities.isNotEmpty) {
      debugPrint('\n⚠️ جهات بدون مدن:');
      for (final region in regionsWithoutCities) {
        debugPrint('   - ${region['name_ar']} (ID: ${region['id']})');
      }
    } else {
      debugPrint('\n✅ جميع الجهات تحتوي على مدن');
    }
    
    // 4. جودة البيانات
    debugPrint('\n🔍 جودة البيانات:');
    debugPrint('   المدن ذات البيانات الناقصة: ${dataQuality['incomplete_count']}');
    debugPrint('   المدن المكررة: ${dataQuality['duplicate_count']}');
    
    if (dataQuality['incomplete_count'] > 0) {
      debugPrint('\n⚠️ مدن ذات بيانات ناقصة:');
      final incompleteCities = dataQuality['incomplete_cities'] as List;
      for (final city in incompleteCities.take(10)) {
        debugPrint('   - ID ${city['id']}: ${city['name_ar']} (${city['status']})');
      }
      if (incompleteCities.length > 10) {
        debugPrint('   ... و ${incompleteCities.length - 10} مدن أخرى');
      }
    }
    
    if (dataQuality['duplicate_count'] > 0) {
      debugPrint('\n⚠️ مدن مكررة:');
      final duplicateCities = dataQuality['duplicate_cities'] as List;
      for (final city in duplicateCities) {
        debugPrint('   - ${city['name_ar']} في الجهة ${city['region_id']}: ${city['count']} مرات');
      }
    }
    
    // 5. تحليل المعرفات
    debugPrint('\n🔢 تحليل المعرفات:');
    final minMax = idAnalysis['min_max'];
    debugPrint('   أقل معرف: ${minMax['min_id']}');
    debugPrint('   أعلى معرف: ${minMax['max_id']}');
    
    final highIdCities = idAnalysis['high_id_cities'] as List;
    debugPrint('\n🎯 المعرفات العالية المطلوبة:');
    for (final city in highIdCities) {
      debugPrint('   ✅ ID ${city['id']}: ${city['name_ar']} (جهة ${city['region_id']})');
    }
    
    final missingHighIds = idAnalysis['missing_high_ids'] as List<int>;
    if (missingHighIds.isNotEmpty) {
      debugPrint('\n❌ معرفات عالية مفقودة:');
      for (final id in missingHighIds) {
        debugPrint('   - ID $id');
      }
    } else {
      debugPrint('\n✅ جميع المعرفات العالية المطلوبة موجودة');
    }
    
    // 6. عينة من البيانات
    debugPrint('\n📋 عينة من بيانات المدن:');
    final sampleData = result['sample_data'] as List;
    for (final city in sampleData.take(15)) {
      debugPrint('   ID ${city['id']}: ${city['name_ar']} | ${city['name_fr']} | ${city['name_en']} (${city['region_name']})');
    }
    if (sampleData.length > 15) {
      debugPrint('   ... و ${sampleData.length - 15} مدينة أخرى');
    }
    
    debugPrint('\n${'=' * 60}');
    debugPrint('✅ انتهى فحص بيانات المدن المغربية');
  }
  
  /// اقتراح حلول للمشاكل المكتشفة
  static Future<List<String>> suggestSolutions() async {
    final result = await checkCitiesData();
    final suggestions = <String>[];
    
    if (result['status'] == 'error') {
      suggestions.add('❌ إصلاح خطأ قاعدة البيانات: ${result['error']}');
      return suggestions;
    }
    
    final summary = result['summary'];
    final distribution = result['distribution'];
    final dataQuality = result['data_quality'];
    final idAnalysis = result['id_analysis'];
    
    // تحليل المشاكل واقتراح الحلول
    if (summary['total_cities'] < summary['current_target']) {
      final missing = summary['current_target'] - summary['total_cities'];
      suggestions.add('📈 إضافة $missing مدينة للوصول للهدف الحالي (${summary['current_target']} مدينة)');
    }
    
    if (summary['total_cities'] < summary['target_cities']) {
      final missing = summary['target_cities'] - summary['total_cities'];
      suggestions.add('🎯 إضافة $missing مدينة للوصول للهدف الأصلي (${summary['target_cities']} مدينة)');
    }
    
    final regionsWithoutCities = distribution['regions_without_cities'] as List;
    if (regionsWithoutCities.isNotEmpty) {
      suggestions.add('🗺️ إضافة مدن للجهات التالية: ${regionsWithoutCities.map((r) => r['name_ar']).join(', ')}');
    }
    
    if (dataQuality['incomplete_count'] > 0) {
      suggestions.add('🔧 إكمال البيانات الناقصة لـ ${dataQuality['incomplete_count']} مدينة');
    }
    
    if (dataQuality['duplicate_count'] > 0) {
      suggestions.add('🔄 حل مشكلة تكرار ${dataQuality['duplicate_count']} مدينة');
    }
    
    final missingHighIds = idAnalysis['missing_high_ids'] as List<int>;
    if (missingHighIds.isNotEmpty) {
      suggestions.add('🔢 إضافة مدن بالمعرفات العالية المطلوبة: ${missingHighIds.join(', ')}');
    }
    
    // اقتراحات تحسين
    suggestions.add('📚 استيراد البيانات من ملف sql-moroccan-cities-master المرجعي');
    suggestions.add('🔍 التحقق من صحة أسماء المدن باللغات الثلاث');
    suggestions.add('📊 إضافة معلومات إضافية (الرمز البريدي، الإحداثيات)');
    suggestions.add('🔗 التحقق من صحة ربط المدن بالجهات');
    
    return suggestions;
  }
}

/// شاشة فحص بيانات المدن
class CitiesDataCheckerScreen extends StatefulWidget {
  const CitiesDataCheckerScreen({super.key});

  @override
  State<CitiesDataCheckerScreen> createState() => _CitiesDataCheckerScreenState();
}

class _CitiesDataCheckerScreenState extends State<CitiesDataCheckerScreen> {
  Map<String, dynamic>? _checkResult;
  List<String>? _suggestions;
  bool _isChecking = false;

  @override
  void initState() {
    super.initState();
    _checkCitiesData();
  }

  Future<void> _checkCitiesData() async {
    setState(() {
      _isChecking = true;
    });

    try {
      final result = await CitiesDataChecker.checkCitiesData();
      final suggestions = await CitiesDataChecker.suggestSolutions();
      
      // طباعة التقرير المفصل في الكونسول
      await CitiesDataChecker.printDetailedReport();
      
      setState(() {
        _checkResult = result;
        _suggestions = suggestions;
      });
    } catch (e) {
      setState(() {
        _checkResult = {
          'status': 'error',
          'error': e.toString(),
        };
      });
    } finally {
      setState(() {
        _isChecking = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('فحص بيانات المدن المغربية'),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _isChecking ? null : _checkCitiesData,
            icon: const Icon(Icons.refresh),
          ),
        ],
      ),
      body: _isChecking
          ? const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('جاري فحص بيانات المدن...'),
                ],
              ),
            )
          : _checkResult == null
              ? const Center(child: Text('لا توجد بيانات'))
              : _buildResultsView(),
    );
  }

  Widget _buildResultsView() {
    if (_checkResult!['status'] == 'error') {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error, color: Colors.red, size: 64),
            const SizedBox(height: 16),
            Text(
              'خطأ في فحص البيانات',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(_checkResult!['error']),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _checkCitiesData,
              child: const Text('إعادة المحاولة'),
            ),
          ],
        ),
      );
    }

    final summary = _checkResult!['summary'];
    final distribution = _checkResult!['distribution'];
    final dataQuality = _checkResult!['data_quality'];

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // الملخص العام
          _buildSummaryCard(summary),
          const SizedBox(height: 16),
          
          // التوزيع الجغرافي
          _buildDistributionCard(distribution),
          const SizedBox(height: 16),
          
          // جودة البيانات
          _buildDataQualityCard(dataQuality),
          const SizedBox(height: 16),
          
          // الاقتراحات
          if (_suggestions != null) _buildSuggestionsCard(_suggestions!),
        ],
      ),
    );
  }

  Widget _buildSummaryCard(Map<String, dynamic> summary) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'الملخص العام',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatCard(
                    'العدد الحالي',
                    '${summary['total_cities']}',
                    'مدينة',
                    Colors.blue,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildStatCard(
                    'الهدف الحالي',
                    '${summary['current_target']}',
                    'مدينة',
                    Colors.green,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildStatCard(
                    'الهدف الأصلي',
                    '${summary['target_cities']}',
                    'مدينة',
                    Colors.orange,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            LinearProgressIndicator(
              value: summary['current_completion_percentage'] / 100,
              backgroundColor: Colors.grey[300],
              valueColor: AlwaysStoppedAnimation<Color>(
                summary['current_completion_percentage'] >= 100 
                  ? Colors.green 
                  : Colors.blue,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'نسبة الإكمال: ${summary['current_completion_percentage']}% من الهدف الحالي',
              style: const TextStyle(fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDistributionCard(Map<String, dynamic> distribution) {
    final citiesPerRegion = distribution['cities_per_region'] as List;
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'التوزيع الجغرافي',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ...citiesPerRegion.map((region) {
              final citiesCount = region['cities_count'] as int;
              final hasCities = citiesCount > 0;
              
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 4),
                child: Row(
                  children: [
                    Icon(
                      hasCities ? Icons.check_circle : Icons.error,
                      color: hasCities ? Colors.green : Colors.red,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(region['region_name']),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: hasCities ? Colors.green.withOpacity(0.1) : Colors.red.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '$citiesCount مدينة',
                        style: TextStyle(
                          color: hasCities ? Colors.green : Colors.red,
                          fontWeight: FontWeight.w600,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ],
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildDataQualityCard(Map<String, dynamic> dataQuality) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'جودة البيانات',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildQualityIndicator(
                    'بيانات ناقصة',
                    dataQuality['incomplete_count'],
                    dataQuality['incomplete_count'] == 0 ? Colors.green : Colors.orange,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildQualityIndicator(
                    'مدن مكررة',
                    dataQuality['duplicate_count'],
                    dataQuality['duplicate_count'] == 0 ? Colors.green : Colors.red,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSuggestionsCard(List<String> suggestions) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'اقتراحات التحسين',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ...suggestions.map((suggestion) {
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 4),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('• ', style: TextStyle(fontSize: 16)),
                    Expanded(child: Text(suggestion)),
                  ],
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(String label, String value, String unit, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Text(
            value,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            unit,
            style: TextStyle(
              fontSize: 12,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildQualityIndicator(String label, int count, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(
            count == 0 ? Icons.check_circle : Icons.warning,
            color: color,
            size: 32,
          ),
          const SizedBox(height: 8),
          Text(
            count.toString(),
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            label,
            style: const TextStyle(fontSize: 12),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
