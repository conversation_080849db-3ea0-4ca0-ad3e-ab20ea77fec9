import 'package:flutter/material.dart';
import '../../shared/themes/app_theme.dart';
import '../../shared/widgets/common_widgets.dart';

/// الصفحة الرئيسية للعملاء
class CustomerHomeScreen extends StatefulWidget {
  const CustomerHomeScreen({super.key});

  @override
  State<CustomerHomeScreen> createState() => _CustomerHomeScreenState();
}

class _CustomerHomeScreenState extends State<CustomerHomeScreen> {
  List<Map<String, dynamic>> _featuredCrafts = [];
  List<Map<String, dynamic>> _topCraftsmen = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // تحميل الحرف المميزة (بيانات مؤقتة)
      _featuredCrafts = [
        {
          'id': 1,
          'name': 'كهربائي',
          'icon': Icons.electrical_services,
          'color': Colors.orange
        },
        {'id': 2, 'name': 'سباك', 'icon': Icons.plumbing, 'color': Colors.blue},
        {
          'id': 3,
          'name': 'نجار',
          'icon': Icons.carpenter,
          'color': Colors.brown
        },
        {
          'id': 4,
          'name': 'حداد',
          'icon': Icons.construction,
          'color': Colors.grey
        },
        {
          'id': 5,
          'name': 'بناء',
          'icon': Icons.home_repair_service,
          'color': Colors.green
        },
        {
          'id': 6,
          'name': 'دهان',
          'icon': Icons.format_paint,
          'color': Colors.purple
        },
      ];

      // تحميل أفضل الحرفيين (بيانات مؤقتة)
      _topCraftsmen = [
        {
          'id': 1,
          'name': 'أحمد محمد',
          'craftType': 'كهربائي',
          'rating': 4.8,
          'completedOrders': 25,
          'isAvailable': true
        },
        {
          'id': 2,
          'name': 'محمد علي',
          'craftType': 'سباك',
          'rating': 4.7,
          'completedOrders': 30,
          'isAvailable': true
        },
        {
          'id': 3,
          'name': 'عبد الله حسن',
          'craftType': 'نجار',
          'rating': 4.9,
          'completedOrders': 40,
          'isAvailable': false
        },
        {
          'id': 4,
          'name': 'يوسف أحمد',
          'craftType': 'حداد',
          'rating': 4.6,
          'completedOrders': 20,
          'isAvailable': true
        },
        {
          'id': 5,
          'name': 'خالد محمود',
          'craftType': 'بناء',
          'rating': 4.8,
          'completedOrders': 35,
          'isAvailable': true
        },
      ];
    } catch (e) {
      debugPrint('خطأ في تحميل البيانات: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF5F5F5),
      appBar: AppBar(
        backgroundColor: const Color(0xFF1976D2),
        elevation: 0,
        title: const Text(
          'الموقف - العملاء',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications_outlined, color: Colors.white),
            onPressed: () {
              // صفحة الإشعارات
            },
          ),
          IconButton(
            icon: const Icon(Icons.person_outline, color: Colors.white),
            onPressed: () {
              // صفحة الملف الشخصي
            },
          ),
        ],
      ),
      body: _isLoading
          ? const CustomLoadingIndicator(message: 'جاري تحميل البيانات...')
          : RefreshIndicator(
              onRefresh: _loadData,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // رسالة ترحيب
                    _buildWelcomeSection(),

                    const SizedBox(height: 24),

                    // شريط البحث
                    _buildSearchBar(),

                    const SizedBox(height: 24),

                    // الحرف المميزة
                    _buildFeaturedCraftsSection(),

                    const SizedBox(height: 24),

                    // أفضل الحرفيين
                    _buildTopCraftsmenSection(),

                    const SizedBox(height: 24),

                    // خدمات سريعة
                    _buildQuickServicesSection(),
                  ],
                ),
              ),
            ),
      bottomNavigationBar: _buildBottomNavigationBar(),
    );
  }

  Widget _buildWelcomeSection() {
    return CustomCard(
      backgroundColor: const Color(0xFF1976D2),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'مرحباً بك في الموقف',
            style: TextStyle(
              color: Colors.white,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'ابحث عن أفضل الحرفيين في مدينتك',
            style: TextStyle(
              color: Colors.white70,
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: CustomButton(
                  text: 'ابحث الآن',
                  backgroundColor: Colors.white,
                  textColor: const Color(0xFF1976D2),
                  onPressed: () {
                    // صفحة البحث
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return CustomTextField(
      hintText: 'ابحث عن حرفي أو خدمة...',
      prefixIcon: Icons.search,
      onTap: () {
        // صفحة البحث
      },
    );
  }

  Widget _buildFeaturedCraftsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'الحرف المميزة',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: AppTheme.textPrimary,
          ),
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 120,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: _featuredCrafts.length,
            itemBuilder: (context, index) {
              final craft = _featuredCrafts[index];
              return Container(
                width: 100,
                margin: const EdgeInsets.only(left: 12),
                child: CustomCard(
                  padding: const EdgeInsets.all(12),
                  onTap: () {
                    // صفحة الحرفة
                  },
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.handyman,
                        size: 32,
                        color: AppTheme.primaryColor,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        craft['name'],
                        style: const TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildTopCraftsmenSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Text(
              'أفضل الحرفيين',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppTheme.textPrimary,
              ),
            ),
            TextButton(
              onPressed: () {
                // صفحة جميع الحرفيين
              },
              child: const Text('عرض الكل'),
            ),
          ],
        ),
        const SizedBox(height: 16),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: _topCraftsmen.length,
          itemBuilder: (context, index) {
            final craftsman = _topCraftsmen[index];
            return CustomCard(
              margin: const EdgeInsets.only(bottom: 12),
              onTap: () {
                // صفحة الحرفي
              },
              child: Row(
                children: [
                  const CircleAvatar(
                    radius: 30,
                    backgroundColor: AppTheme.primaryColor,
                    child: Icon(
                      Icons.person,
                      color: Colors.white,
                      size: 30,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          craftsman['name'],
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          craftsman['craftType'] ?? '',
                          style: const TextStyle(
                            color: AppTheme.textSecondary,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Row(
                          children: [
                            const Icon(
                              Icons.star,
                              color: Colors.amber,
                              size: 16,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              '${craftsman['rating']?.toStringAsFixed(1) ?? '0.0'}',
                              style: const TextStyle(
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            const SizedBox(width: 8),
                            Text(
                              '(${craftsman['completedOrders']} طلب)',
                              style: const TextStyle(
                                color: AppTheme.textSecondary,
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: craftsman['isAvailable'] == true
                          ? Colors.green.shade100
                          : Colors.red.shade100,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      craftsman['isAvailable'] == true ? 'متاح' : 'مشغول',
                      style: TextStyle(
                        color: craftsman['isAvailable'] == true
                            ? Colors.green.shade700
                            : Colors.red.shade700,
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildQuickServicesSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'خدمات سريعة',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: AppTheme.textPrimary,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: CustomButton(
                text: 'طلب عاجل',
                icon: Icons.flash_on,
                backgroundColor: Colors.orange,
                onPressed: () {
                  // صفحة الطلب العاجل
                },
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: CustomButton(
                text: 'حجز موعد',
                icon: Icons.calendar_today,
                backgroundColor: AppTheme.primaryColor,
                onPressed: () {
                  // صفحة حجز الموعد
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildBottomNavigationBar() {
    return BottomNavigationBar(
      type: BottomNavigationBarType.fixed,
      selectedItemColor: const Color(0xFF1976D2),
      unselectedItemColor: Colors.grey,
      items: const [
        BottomNavigationBarItem(
          icon: Icon(Icons.home),
          label: 'الرئيسية',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.search),
          label: 'البحث',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.bookmark),
          label: 'المفضلة',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.history),
          label: 'طلباتي',
        ),
        BottomNavigationBarItem(
          icon: Icon(Icons.person),
          label: 'الملف الشخصي',
        ),
      ],
    );
  }
}
