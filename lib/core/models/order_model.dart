/// حالات الطلب
enum OrderStatus {
  pending,    // في الانتظار
  accepted,   // مقبول
  inProgress, // قيد التنفيذ
  completed,  // مكتمل
  cancelled,  // ملغي
  rejected,   // مرفوض
}

/// نموذج الطلب
class OrderModel {
  final int? id;
  final int customerId;
  final int craftsmanId;
  final String title;
  final String description;
  final OrderStatus status;
  final double? price;
  final DateTime? scheduledDate;
  final String? customerNotes;
  final String? craftsmanNotes;
  final double? customerRating;
  final String? customerReview;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final DateTime? completedAt;

  OrderModel({
    this.id,
    required this.customerId,
    required this.craftsmanId,
    required this.title,
    required this.description,
    this.status = OrderStatus.pending,
    this.price,
    this.scheduledDate,
    this.customerNotes,
    this.craftsmanNotes,
    this.customerRating,
    this.customerReview,
    required this.createdAt,
    this.updatedAt,
    this.completedAt,
  });

  /// إنشاء نموذج من Map
  factory OrderModel.fromMap(Map<String, dynamic> map) {
    return OrderModel(
      id: map['id']?.toInt(),
      customerId: map['customer_id']?.toInt() ?? 0,
      craftsmanId: map['craftsman_id']?.toInt() ?? 0,
      title: map['title'] ?? '',
      description: map['description'] ?? '',
      status: _parseStatus(map['status']),
      price: map['price']?.toDouble(),
      scheduledDate: map['scheduled_date'] != null ? DateTime.parse(map['scheduled_date']) : null,
      customerNotes: map['customer_notes'],
      craftsmanNotes: map['craftsman_notes'],
      customerRating: map['customer_rating']?.toDouble(),
      customerReview: map['customer_review'],
      createdAt: DateTime.parse(map['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: map['updated_at'] != null ? DateTime.parse(map['updated_at']) : null,
      completedAt: map['completed_at'] != null ? DateTime.parse(map['completed_at']) : null,
    );
  }

  /// تحويل النموذج إلى Map
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'customer_id': customerId,
      'craftsman_id': craftsmanId,
      'title': title,
      'description': description,
      'status': status.name,
      'price': price,
      'scheduled_date': scheduledDate?.toIso8601String(),
      'customer_notes': customerNotes,
      'craftsman_notes': craftsmanNotes,
      'customer_rating': customerRating,
      'customer_review': customerReview,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'completed_at': completedAt?.toIso8601String(),
    };
  }

  /// تحليل حالة الطلب من النص
  static OrderStatus _parseStatus(String? statusString) {
    switch (statusString?.toLowerCase()) {
      case 'pending':
        return OrderStatus.pending;
      case 'accepted':
        return OrderStatus.accepted;
      case 'inprogress':
      case 'in_progress':
        return OrderStatus.inProgress;
      case 'completed':
        return OrderStatus.completed;
      case 'cancelled':
        return OrderStatus.cancelled;
      case 'rejected':
        return OrderStatus.rejected;
      default:
        return OrderStatus.pending;
    }
  }

  /// إنشاء نسخة محدثة من النموذج
  OrderModel copyWith({
    int? id,
    int? customerId,
    int? craftsmanId,
    String? title,
    String? description,
    OrderStatus? status,
    double? price,
    DateTime? scheduledDate,
    String? customerNotes,
    String? craftsmanNotes,
    double? customerRating,
    String? customerReview,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? completedAt,
  }) {
    return OrderModel(
      id: id ?? this.id,
      customerId: customerId ?? this.customerId,
      craftsmanId: craftsmanId ?? this.craftsmanId,
      title: title ?? this.title,
      description: description ?? this.description,
      status: status ?? this.status,
      price: price ?? this.price,
      scheduledDate: scheduledDate ?? this.scheduledDate,
      customerNotes: customerNotes ?? this.customerNotes,
      craftsmanNotes: craftsmanNotes ?? this.craftsmanNotes,
      customerRating: customerRating ?? this.customerRating,
      customerReview: customerReview ?? this.customerReview,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      completedAt: completedAt ?? this.completedAt,
    );
  }

  @override
  String toString() {
    return 'OrderModel(id: $id, customerId: $customerId, craftsmanId: $craftsmanId, title: $title, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is OrderModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  /// الحصول على نص حالة الطلب بالعربية
  String get statusText {
    switch (status) {
      case OrderStatus.pending:
        return 'في الانتظار';
      case OrderStatus.accepted:
        return 'مقبول';
      case OrderStatus.inProgress:
        return 'قيد التنفيذ';
      case OrderStatus.completed:
        return 'مكتمل';
      case OrderStatus.cancelled:
        return 'ملغي';
      case OrderStatus.rejected:
        return 'مرفوض';
    }
  }

  /// الحصول على لون حالة الطلب
  String get statusColor {
    switch (status) {
      case OrderStatus.pending:
        return '#FFA500'; // برتقالي
      case OrderStatus.accepted:
        return '#2196F3'; // أزرق
      case OrderStatus.inProgress:
        return '#FF9800'; // برتقالي داكن
      case OrderStatus.completed:
        return '#4CAF50'; // أخضر
      case OrderStatus.cancelled:
        return '#9E9E9E'; // رمادي
      case OrderStatus.rejected:
        return '#F44336'; // أحمر
    }
  }

  /// التحقق من إمكانية إلغاء الطلب
  bool get canBeCancelled {
    return status == OrderStatus.pending || status == OrderStatus.accepted;
  }

  /// التحقق من إمكانية تقييم الطلب
  bool get canBeRated {
    return status == OrderStatus.completed && customerRating == null;
  }

  /// الحصول على نص السعر
  String get priceText {
    if (price == null) return 'غير محدد';
    return '${price!.toStringAsFixed(2)} درهم';
  }

  /// التحقق من صحة البيانات
  bool get isValid {
    return customerId > 0 &&
        craftsmanId > 0 &&
        title.isNotEmpty &&
        description.isNotEmpty;
  }
}
