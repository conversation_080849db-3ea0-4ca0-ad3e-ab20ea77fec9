# تطبيق الموقف - منصة الحرفيين

تطبيق متعدد المنصات يهدف إلى ربط العملاء بالحرفيين والصناع التقليديين في المدن السعودية.

## نظرة عامة

الموقف هو تطبيق مبني بتقنية Flutter يوفر منصة شاملة تجمع بين:
- **العملاء**: الذين يبحثون عن خدمات حرفية متخصصة
- **الحرفيين**: أصحاب المهن والحرف التقليدية والحديثة

## الميزات الرئيسية

### للعملاء:
- البحث عن الحرفيين حسب نوع الحرفة والموقع
- عرض ملفات الحرفيين الشخصية وأعمالهم السابقة
- طلب الخدمات وتتبع حالة الطلبات
- نظام تقييم ومراجعة الحرفيين
- التواصل المباشر مع الحرفيين

### للحرفيين:
- إنشاء ملف شخصي مهني
- عرض أعمال سابقة ومهارات
- استقبال وإدارة الطلبات
- تحديد أوقات العمل والتوفر
- بناء سمعة مهنية من خلال التقييمات

## التقنيات المستخدمة

- **Flutter**: إطار العمل الأساسي للتطبيق
- **Dart**: لغة البرمجة
- **Material Design 3**: نظام التصميم
- **دعم متعدد المنصات**: Android, iOS, Web, Windows, macOS, Linux

## هيكل المشروع

```
lib/
├── core/                 # الوظائف الأساسية
│   ├── constants/       # الثوابت
│   ├── utils/          # الأدوات المساعدة
│   ├── services/       # الخدمات
│   └── network/        # إعدادات الشبكة
├── features/           # ميزات التطبيق
│   ├── auth/          # المصادقة
│   ├── home/          # الصفحة الرئيسية
│   ├── profile/       # الملف الشخصي
│   ├── craftsmen/     # إدارة الحرفيين
│   ├── orders/        # إدارة الطلبات
│   └── chat/          # المحادثات
└── shared/            # المكونات المشتركة
    ├── widgets/       # الواجهات المشتركة
    ├── models/        # نماذج البيانات
    └── themes/        # الثيمات والألوان
```

## البدء

### المتطلبات
- Flutter SDK (3.24.5 أو أحدث)
- Dart SDK (3.5.4 أو أحدث)
- Android Studio أو VS Code
- Git

### التثبيت

1. استنساخ المشروع:
```bash
git clone https://github.com/yourusername/elmoqef_app.git
cd elmoqef_app
```

2. تثبيت التبعيات:
```bash
flutter pub get
```

3. تشغيل التطبيق:
```bash
flutter run
```

## الحالة الحالية

المشروع في مرحلة الإعداد الأولي مع:
- ✅ هيكل المشروع الأساسي
- ✅ نظام الثيمات والألوان
- ✅ الثوابت والأدوات المساعدة
- ✅ نماذج البيانات الأساسية
- ⏳ الصفحات والواجهات (قيد التطوير)
- ⏳ نظام المصادقة (قيد التطوير)
- ⏳ قاعدة البيانات والAPI (قيد التطوير)

## المساهمة

نرحب بالمساهمات! يرجى اتباع الخطوات التالية:

1. Fork المشروع
2. إنشاء فرع للميزة الجديدة (`git checkout -b feature/AmazingFeature`)
3. Commit التغييرات (`git commit -m 'Add some AmazingFeature'`)
4. Push إلى الفرع (`git push origin feature/AmazingFeature`)
5. فتح Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## التواصل

- البريد الإلكتروني: <EMAIL>
- الموقع الإلكتروني: https://elmoqef.com
